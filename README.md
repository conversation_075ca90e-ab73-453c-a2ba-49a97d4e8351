[![CI](https://github.com/moolah-finance/website/actions/workflows/blank.yml/badge.svg)](https://github.com/moolah-finance/website/actions/workflows/blank.yml)
[![Lint](https://github.com/moolah-finance/website/actions/workflows/main.yml/badge.svg)](https://github.com/moolah-finance/website/actions/workflows/main.yml)
[![node.js CI](https://github.com/moolah-finance/website/actions/workflows/node.js.yml/badge.svg)](https://github.com/moolah-finance/website/actions/workflows/node.js.yml)

### Moolah.capital web site

#### Built using react web tools to showcase Moolah system features and functions
#### Deployed on Heroku initially
#### No CI/CD process except for some Github actions to review and check code
 
**TODO**

- [ ] document pages
- [ ] check site UX on multiple devices & browser versions
- [ ] list content providers(to do)
- [ ] list items to be added in /issues
- [ ] review site security
- [ ] add SEO optimization
- [ ] link site to other connected sites like medium.com, substack, linkedIn etc 
