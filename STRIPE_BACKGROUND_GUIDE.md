# Stripe Background Effects - Control Guide

## 🎛️ **Quick Controls**

### **Turn On/Off Globally**
```jsx
// In any component
<StripeLikeBackground enabled={false} />  // Disable
<StripeLikeBackground enabled={true} />   // Enable
```

### **Use Presets (Recommended)**
```jsx
import { applyPreset } from '../Common/StripeBackgroundConfig';

// Easy presets
<StripeLikeBackground {...applyPreset('SUBTLE')} />
<StripeLikeBackground {...applyPreset('MEDIUM')} />
<StripeLikeBackground {...applyPreset('BOLD')} />
<StripeLikeBackground {...applyPreset('HOMEPAGE')} />
```

## 🎨 **4 Shape Variants**

### **Variant 1: Corner Triangles**
```jsx
<StripeLikeBackground className="stripe-bg-variant-1" />
```
- Purple triangle top-left
- Cyan triangle bottom-right
- Best for: Clean, minimal sections

### **Variant 2: Side Waves**
```jsx
<StripeLikeBackground className="stripe-bg-variant-2" />
```
- Curved waves on left/right edges
- Best for: Wide content sections

### **Variant 3: Diagonal Stripes**
```jsx
<StripeLikeBackground className="stripe-bg-variant-3" />
```
- Angled bands top/bottom
- Best for: Dynamic, modern feel

### **Variant 4: Corner Circles**
```jsx
<StripeLikeBackground className="stripe-bg-variant-4" />
```
- Small circles in corners
- Best for: Subtle, elegant sections

## 🌐 **Adding to Pages**

### **Method 1: Target Specific Background Colors**
```jsx
// Automatically add to all medium-blue sections
{section.backgroundColor === COLORS.MEDIUM_BLUE && (
  <StripeLikeBackground {...applyPreset('MEDIUM')} />
)}
```

### **Method 2: Manual Placement**
```jsx
// Add to specific sections
<section style={{ position: 'relative', backgroundColor: '#f7f9fc' }}>
  <StripeLikeBackground {...applyPreset('ABOUT')} />
  {/* Your content */}
</section>
```

### **Method 3: Cycling Variants**
```jsx
// Different effect for each section
{sections.map((section, index) => (
  <section key={index} style={{ position: 'relative' }}>
    {section.backgroundColor === COLORS.MEDIUM_BLUE && (
      <StripeLikeBackground 
        className={getVariantClass(index)}
        opacity={0.6}
        animationDuration="30s"
      />
    )}
  </section>
))}
```

## ⚙️ **Custom Configuration**

### **Change Colors Globally**
Edit `src/constants/colors.css`:
```css
:root {
  --button-red: #your-purple-color;
  --frame-color: #your-cyan-color;
}
```

### **Create Custom Variants**
Add to `src/Components/Common/StripeLikeBackground.css`:
```css
.stripe-bg-variant-5 .stripe-like-bg-effect {
  background: /* your custom gradients */;
  animation: /* your custom animation */;
}
```

### **Page-Specific Presets**
Add to `StripeBackgroundConfig.js`:
```js
CONTACT: {
  opacity: 0.5,
  animationDuration: "50s",
  className: "stripe-bg-variant-1"
}
```

## 📄 **Examples for Each Page**

### **Homepage** (Already implemented)
```jsx
{section.backgroundColor === HOMEPAGE_COLORS.MEDIUM_BLUE && (
  <StripeLikeBackground 
    className={`stripe-bg-variant-${(index % 4) + 1}`}
    opacity={0.8}
    animationDuration={index % 2 === 0 ? "35s" : "28s"}
  />
)}
```

### **About Page**
```jsx
// Add to medium-blue sections
<StripeLikeBackground {...applyPreset('ABOUT')} />
```

### **Funds Page**
```jsx
// Add to specific sections
<StripeLikeBackground {...applyPreset('FUNDS')} />
```

### **Strategies Page**
```jsx
// Add to alternating sections
<StripeLikeBackground {...applyPreset('STRATEGIES')} />
```

## 🛠️ **Troubleshooting**

### **Text Not Readable?**
- Lower opacity: `opacity={0.3}`
- Use variant 1 or 4 (corner-only effects)
- Check section has `position: relative`

### **Animation Too Fast/Slow?**
- Slower: `animationDuration="50s"`
- Faster: `animationDuration="15s"`

### **Want Different Colors?**
- Use custom colors: `colors={['#ff6b6b', '#4ecdc4']}`
- Or modify global CSS variables

### **Effect Not Showing?**
- Check `enabled={true}`
- Ensure parent has `position: relative`
- Verify CSS file is imported

## 🚀 **Quick Start for New Pages**

1. **Import the component:**
```jsx
import StripeLikeBackground from '../Common/StripeLikeBackground.jsx';
import { applyPreset } from '../Common/StripeBackgroundConfig';
```

2. **Add to sections with grey background:**
```jsx
<section style={{ position: 'relative', backgroundColor: '#f7f9fc' }}>
  <StripeLikeBackground {...applyPreset('MEDIUM')} />
  {/* Your content */}
</section>
```

3. **Customize as needed:**
```jsx
<StripeLikeBackground 
  {...applyPreset('MEDIUM')} 
  opacity={0.4}  // Override preset
/>
```
