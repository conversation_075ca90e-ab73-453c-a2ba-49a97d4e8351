const path = require('path');

module.exports = function override(config, env) {
  // Keep existing rule for .mjs files
  config.module.rules.push({
    test: /\.mjs$/,
    include: /node_modules/,
    type: 'javascript/auto',
  });

  // Add a rule to exclude files in the deprecated folder
  config.module.rules.push({
    test: /\.jsx?$/,
    include: path.resolve(__dirname, 'src/Components/Deprecated'),
    use: 'null-loader',
  });

  // Add a webpack plugin to ignore the deprecated folder during build
  if (!config.plugins) {
    config.plugins = [];
  }

  // Add a webpack ignore plugin to exclude the deprecated folder
  const IgnorePlugin = require('webpack').IgnorePlugin;
  config.plugins.push(
    new IgnorePlugin({
      resourceRegExp: /^\.\/.*$/,
      contextRegExp: /src\/Components\/Deprecated/,
    })
  );

  return config;
};
