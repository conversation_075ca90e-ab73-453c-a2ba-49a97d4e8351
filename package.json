{"name": "moolah_capital", "version": "0.1.0", "private": true, "engines": {"node": "=20.X", "npm": ">=10.X"}, "sideEffects": false, "dependencies": {"@babel/eslint-parser": "^7.26.10", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@emailjs/browser": "^4.4.1", "bootstrap": "^5.3.4", "chart.js": "^4.4.9", "framer-motion": "^10.18.0", "globals": "^16.0.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-ga4": "^2.1.0", "react-google-recaptcha": "^3.1.0", "react-helmet": "^6.1.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.5.2", "react-router": "^7.4.1", "react-router-dom": "^6.3.0", "react-scripts": "^5.0.1", "react-scroll": "^1.9.3", "serve": "^14.2.4", "web-vitals": "^4.2.4"}, "devDependencies": {"@babel/eslint-parser": "^7.26.10", "@babel/preset-react": "^7.26.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "null-loader": "^4.0.1", "react-app-rewired": "^2.2.1", "stylelint": "^16.17.0", "stylelint-config-standard": "^37.0.0"}, "scripts": {"lint:css": "stylelint \"**/*.css\"", "devstart": "react-app-rewired start", "start": "serve -s build", "build": "react-app-rewired build", "heroku-postbuild": "react-app-rewired build", "test": "react-app-rewired test", "minify": "terser static/js/bundle.js -o static/js/bundle.min.js --compress --mangle", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "babel": {"plugins": ["@babel/plugin-proposal-private-property-in-object"]}}