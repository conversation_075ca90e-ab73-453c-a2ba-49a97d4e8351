const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Create the icons directory if it doesn't exist
const iconsDir = path.join(__dirname, '../public/icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Source image - using an existing image from the public directory
// You can replace this with any suitable image
const sourceImage = path.join(__dirname, '../public/favicon.ico');

// Define the icon sizes to generate
const iconSizes = [
  { name: 'favicon-16x16.png', size: 16 },
  { name: 'favicon-32x32.png', size: 32 },
  { name: 'android-chrome-192x192.png', size: 192 },
  { name: 'android-chrome-512x512.png', size: 512 }
];

// Generate each icon
async function generateIcons() {
  try {
    for (const icon of iconSizes) {
      const outputPath = path.join(iconsDir, icon.name);
      
      await sharp(sourceImage)
        .resize(icon.size, icon.size)
        .toFile(outputPath);
      
    }
    
  } catch (error) {
    console.error('Error generating icons:', error);
  }
}

generateIcons();
