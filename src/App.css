.tabs-container {
  width: 100%;
  margin-top: 2rem;
}

.tab-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  border-bottom: 1px solid var(--white-30);
  padding-bottom: 0.5rem;
  margin-bottom: 1.5rem;
}

.tab-button {
  background: none;
  border: none;
  outline: none;
  font-family: var(--font-body);
  font-size: 1rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  cursor: pointer;
  color: var(--text-white);
  border-bottom: 3px solid transparent;
  transition: border-color 0.3s, color 0.3s;
}

.tab-button:hover {
  color: var(--button-red);
  background-color: rgba(139, 30, 45, 0.05);
  transform: scale(1.02);
}

.tab-button.active-tab {
  color: var(--button-red);
  border-bottom: 3px solid var(--button-red);
  background-color: rgba(139, 30, 45, 0.05);
}

.tab-content {
  padding: 1rem 0;
}

.app-container {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--container-padding-x);
  text-align: center;
}

.tabs {
  text-align: left;
}

/* Consistent h3 header styling across all tabs */
.tab-content h3 {
  display: flex;
  align-items: center;
  font-family: var(--font-header);
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-white);
  text-align: center;
  justify-content: center;
}

/* Overview tab styles */
.overview-container {
  text-align: left;
  padding: var(--container-padding-x) 0;
}

.fund-header {
  margin-bottom: 25px;
}

.fund-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
}

.meta-item {
  background-color: var(--white-10);
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
}

.risk-meter {
  margin: 15px 0;
}

.risk-bar {
  height: 10px;
  background-color: var(--white-20);
  border-radius: 5px;
  position: relative;
  margin-bottom: 5px;
}

.risk-indicator {
  position: absolute;
  height: 20px;
  width: 20px;
  background-color: var(--error-red);
  border-radius: 50%;
  top: -5px;
  transform: translateX(-50%);
}

.risk-indicator.low {
  left: 16.7%;
}

.risk-indicator.medium {
  left: 50%;
  background-color: var(--warning-yellow);
}

.risk-indicator.high {
  left: 83.3%;
  background-color: var(--red);
}

.risk-labels {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

/* Performance tab styles */
.performance-container {
  padding: 20px 0;
}

.performance-metrics {
  margin-top: 30px;
}

.chart-note {
  text-align: center;
  font-size: 0.9rem;
  color: var(--white-30);
  margin-top: 10px;
}

.positive-return {
  color: var(--success-green);
  font-weight: bold;
}

.negative-return {
  color: var(--error-red);
  font-weight: bold;
}

/* Ensure charts are side by side */
.charts-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 30px 0;
  width: 100%;
}

.chart-box {
  width: 48%;
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--dark-blue);
  border-radius: 8px;
  box-shadow: 0 2px 4px var(--black-10);
  box-sizing: border-box;
}

@media (max-width: 768px) {
  .charts-container {
    flex-direction: column;
  }
  
  .chart-box {
    width: 100%;
  }
}

.text-card {
  font-family: var(--font-body);
}

/* GenAI tagline section with reduced padding */
#genai-tagline {
  padding: var(--container-padding-y) var(--container-padding-x-large) !important;
}

/* Force consistent container behavior for all fund sections */
#funds-intro .container,
#smartbeta .container,
#investment-process .container,
#funds-cta .container {
  padding-left: var(--container-padding-x) !important;
  padding-right: var(--container-padding-x) !important;
  margin-left: auto !important;
  margin-right: auto !important;
  max-width: 1200px !important;
}

/* White accordion chevrons across all pages */
.accordion-button::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e") !important;
}

.accordion-button:not(.collapsed)::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e") !important;
}

/* Fix accordion animation direction - force slide down */
.accordion-collapse {
  transition: height 0.35s ease !important;
}

.accordion-collapse.collapsing {
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease !important;
}

.accordion-collapse.show {
  height: auto !important;
}
