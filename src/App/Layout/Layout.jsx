import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import Header from '../Header/Header.jsx';
import Footer from '../Footer/Footer.jsx';
import LoadingSpinner from '../../Components/Common/LoadingSpinner.jsx';

const Layout = ({ children }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isFadingOut, setIsFadingOut] = useState(false);
  const location = useLocation();
  const isContactPage = location.pathname === '/contact';
  const isHomePage = location.pathname === '/';

  useEffect(() => {
    // Set data-path attribute on body for CSS targeting
    document.body.setAttribute('data-path', location.pathname);
    
    // Reset scroll position when navigating
    window.scrollTo(0, 0);
    
    // Handle loading state
    setIsFadingOut(true);
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [location.pathname]);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className={`page px-0 ${isHomePage ? 'home-page' : ''}`} style={{
      margin: 0,
      padding: 0,
      backgroundColor: isHomePage ? 'transparent' : null
    }}>
      <Header />
      
      {isVisible && (
        <button
          className="border px-3 py-2 bi bi-chevron-double-up"
          onClick={scrollToTop}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              scrollToTop();
            }
          }}
          aria-label="Scroll to top"
          style={{
            position: 'fixed',
            zIndex: '1000',
            bottom: 'var(--container-padding-x)',
            right: 'var(--container-padding-x)',
            backgroundColor: 'white',
            color: 'var(--primary)',
            border: 'none',
            borderRadius: 'var(--border-radius-sm)',
            cursor: 'pointer',
          }}
        />
      )}
      
      <div className="content position-relative" style={{
        paddingTop: isHomePage ? '0' : (window.innerWidth <= 768 ? 'var(--spacing-section)' : '0'),
        marginTop: '0',
        backgroundColor: 'transparent',
        position: 'relative',
        zIndex: 1
      }}>
        {isLoading && !isContactPage && <LoadingSpinner isFadingOut={isFadingOut} />}
        {children}
        <Footer />
      </div>
    </div>
  );
}

export default Layout;
