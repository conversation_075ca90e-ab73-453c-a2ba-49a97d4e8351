import React, { useState, useRef, useEffect } from 'react';
import './Tabs.css';

const Tabs = ({ children, defaultTab }) => {
  const childrenArray = React.Children.toArray(children);
  const labels = childrenArray
    .filter((child) => !!child.props.label)
    .map((child) => child.props.label);

  const [activeTab, setActiveTab] = useState(defaultTab || labels[0]);
  const tabsHeaderRef = useRef(null);
  const activeButtonRef = useRef(null);
  const isMobile = window.innerWidth <= 768;

  // Update the indicator position when active tab changes
  useEffect(() => {
    if (tabsHeaderRef.current && activeButtonRef.current) {
      const headerElement = tabsHeaderRef.current;
      const activeElement = activeButtonRef.current;

      // Get positions for the sliding indicator
      const headerRect = headerElement.getBoundingClientRect();
      const activeRect = activeElement.getBoundingClientRect();

      // Set the custom property for the indicator width and position
      headerElement.style.setProperty('--indicator-left', `${activeRect.left - headerRect.left}px`);
      headerElement.style.setProperty('--indicator-width', `${activeRect.width}px`);
    }
  }, [activeTab]);

  // Update indicator position on window resize to handle responsive layouts
  useEffect(() => {
    const handleResize = () => {
      if (tabsHeaderRef.current && activeButtonRef.current) {
        const headerElement = tabsHeaderRef.current;
        const activeElement = activeButtonRef.current;

        const headerRect = headerElement.getBoundingClientRect();
        const activeRect = activeElement.getBoundingClientRect();

        headerElement.style.setProperty('--indicator-left', `${activeRect.left - headerRect.left}px`);
        headerElement.style.setProperty('--indicator-width', `${activeRect.width}px`);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [activeTab]);

  // Handle tab change with animation
  const handleTabChange = (label) => {
    setActiveTab(label);
  };

  return (
    <div className="tabs-container">
      <div className="tabs-header" ref={tabsHeaderRef}>
        {labels.map((label) => (
          <button
            className={`tab-button ${label === activeTab ? 'active-tab' : ''}`}
            key={label}
            onClick={() => handleTabChange(label)}
            ref={label === activeTab ? activeButtonRef : null}
          >
            {label}
          </button>
        ))}
      </div>

      <div className="tabs-body">
        {childrenArray.map((child) =>
          child.props.label === activeTab ? child : null
        )}
      </div>
    </div>
  );
};

export default Tabs;
