import React, { useState, useEffect, useMemo } from 'react';
import useAssetCache from '../../context/useAssetCache.js';
import CTASection from '../../App/CTA/CTASection.jsx';
import useFadeInOnScroll from '../../hooks/useFadeInOnScroll';
import useRefsArray from '../../hooks/useRefsArray';
import useVisibilityObserver from '../../hooks/useVisibilityObserver';

// Removed FullHeightHero - using regular section instead
import SectionFrame from '../Common/SectionFrame';
import SectionFrame2 from '../Common/SectionFrame2';

import { applyPreset } from '../Common/StripeBackgroundConfig';
import '../Common/StripeLikeBackground.css'; // Import CSS for fx-bubbles

// Import global colors
import { COLORS } from '../../constants/colors';

// Use global colors directly
const ABOUT_COLORS = COLORS;

const About = () => {
  const imgAbout = useAssetCache('imgAbout', '/about-background.png');
  const [imageLoaded, setImageLoaded] = useState(false);
  const [ref2, isVisible2] = useFadeInOnScroll();
  const [ref3, isVisible3] = useFadeInOnScroll();
  const [ref4, isVisible4] = useFadeInOnScroll();

  // Make sure all history items have a valid theme
  const historyList = useMemo(() => [
    { theme: "launch", number: 1, title: "In the beginning", content: "Moolah Capital was founded by two finance sector veterans who combined their expertise in business, technology, and analytics to tackle the challenges of crypto investing. With backgrounds in traditional finance, including stints at Barclays Investment Bank, Bank of America, and Lloyds Bank, they were well-equipped to analyze the crypto market." },
    { theme: "problem", number: 2, title: "Identifying The Problem", content: "The team recognized that crypto investing was fraught with risks, including volatile markets, untrustworthy advisors, and confusing marketing. The steep learning curve required to keep up with new coins and protocols made it difficult for most people to invest in crypto." },
    { theme: "team", number: 3, title: "A New Approach", content: "Moolah team applied standard techniques from equity, forex, and rates markets to investigate the crypto market. They analyzed multiple coins, verifying their characteristics, such as volatility, liquidity, correlation to other assets, and historical duration. This fundamental analysis led to the selection of a candidate set of coins that generated stable returns with a good risk trade-off." },
    { theme: "technology", number: 4, title: "Developing Trading Signals", content: "Moolah team applied standard techniques from equity, forex, and rates markets to investigate the crypto market. They analyzed multiple coins, verifying their characteristics, such as volatility, liquidity, correlation to other assets, and historical duration. This fundamental analysis led to the selection of a candidate set of coins that generated stable returns with a good risk trade-off." },
    { theme: "learning", number: 5, title: "Continuous Learning", content: "Moolah team applied standard techniques from equity, forex, and rates markets to investigate the crypto market. They analyzed multiple coins, verifying their characteristics, such as volatility, liquidity, correlation to other assets, and historical duration. This fundamental analysis led to the selection of a candidate set of coins that generated stable returns with a good risk trade-off." },
    { theme: "solution", number: 6, title: "The Future of Crypto Investing", content: "Moolah Capital plans to offer a core set of funds under three thematic approaches: Passive, Smart Beta, and Special Situations. The team continues to search for new fund ideas, evaluating them using the same rigorous methodology. Follow Moolah Capital's News page and social media channels for updates on their journey to revolutionize crypto investing." },
  ], []);

  // Let's log the themes to debug
  useEffect(() => {
    console.log("History themes:", historyList.map(item => item.theme));
  }, [historyList]);

  const convictionList = [
    {
      id: 1,
      title: "Access to all Markets",
      img: '/about-background.png',
      description: "Our strategies span the full spectrum of the digital asset landscape - from blue-chip cryptocurrencies to emerging DeFi protocols—providing unparalleled access to global crypto innovation across crypto exchanges, lending platforms, NFTs, and more."
    },
    {
      id: 2,
      title: "Diversification lowers risk",
      img: '/moolah-about-diversification.jpg',
      description: "We build diversified portfolios that mirror the characteristics of index funds while applying smart filters for liquidity, volatility, and correlation. This reduces exposure to any single asset and enhances long-term stability across market cycles."
    },
    {
      id: 3,
      title: "Disruption creates opportunities",
      img: '/moolah-about-opportunities.jpg',
      description: "We identify and invest early in disruptive crypto technologies—such as L2 scaling, cross-chain interoperability, and tokenized real-world assets—that have the potential to reshape traditional finance and generate outsized returns."
    },
    {
      id: 4,
      title: "Growth in adoption and usage",
      img: '/img-08.jpg',
      description: "We track user growth, transaction volume, and developer activity to allocate capital toward protocols and platforms gaining traction. Increased adoption drives demand—and in turn, long-term price appreciation—for high-utility tokens."
    },
    {
      id: 5,
      title: "Active management & learning",
      img: '/img-07.jpg',
      description: "Our adaptive strategies use data-driven insights to navigate market volatility, rebalance exposure, and learn continuously from evolving macro and on-chain signals. We actively optimize positions to stay ahead of shifting dynamics."
    },
    {
      id: 6,
      title: "Decentralized long-term rewards",
      img: '/img-09.jpg',
      description: "We invest in protocols that incentivize long-term participation through staking, governance, and token rewards. These decentralized models offer resilient income streams and align well with long-horizon investment objectives."
    }
  ];

  const convictionRefs = useRefsArray(convictionList.length);
  const [convictionVisible, setConvictionVisible] = useState(convictionList.map(() => false));
  useVisibilityObserver(convictionRefs, setConvictionVisible);



  // Handle image loading
  useEffect(() => {
    if (!imgAbout?.src) return;

    const img = new Image();
    img.onload = () => setImageLoaded(true);
    img.onerror = () => setImageLoaded(true);
    img.src = imgAbout.src;

    if (img.complete) {
      setImageLoaded(true);
    }
  }, [imgAbout?.src]);

  return (
    <div className="text-black">
      {/* CSS Override for About page hero title */}
      <style>
        {`
          /* CSS to eliminate white margins */
          .about_main {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
          }

          #about-cta {
            margin-bottom: 0 !important;
          }

          .full-width-medium-section {
            margin-bottom: 0 !important;
          }
        `}
      </style>

      {/* Hero Section - Static background like GenAI */}
      <section style={{
        backgroundColor: COLORS.DARK_BLUE,
        minHeight: '70vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative'
      }}>
        {/* Floating bubbles for hero section */}
        <div className="fx-bubbles" aria-hidden="true" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 0,
          pointerEvents: 'none'
        }}>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
        </div>

        <div className="container text-center" style={{ position: 'relative', zIndex: 1 }}>
          <h1 className="display-4 text-white mb-4 mobile-header">
            About Moolah Capital
          </h1>
          <p className="lead text-white mb-5">Our story, mission, and values</p>
        </div>
      </section>



        {/* Why Choose Moolah Capital Section - Copied from Homepage */}
        <section className="full-width-medium-section" style={{
          backgroundColor: ABOUT_COLORS.MEDIUM_BLUE,
          paddingTop: 'var(--section-padding-y)',
          paddingBottom: 'var(--section-padding-y)',
          paddingLeft: '0',
          paddingRight: '0',
          position: 'relative'
        }}>
          {/* Floating bubbles */}
          <div className="fx-bubbles" aria-hidden="true" style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 0,
            pointerEvents: 'none'
          }}>
            <span className="b"></span>
            <span className="b"></span>
            <span className="b"></span>
            <span className="b"></span>
            <span className="b"></span>
          </div>

          <div className="container" style={{ paddingLeft: 'var(--container-padding-x)', paddingRight: 'var(--container-padding-x)', position: 'relative', zIndex: 1 }}>
            <div className="row justify-content-center mb-5">
              <div className="col-lg-10 text-center">
                <h2 className="h2 mb-4" style={{ color: ABOUT_COLORS.TEXT_BLACK }}>Our Journey</h2>
                <p className='body-text text-center mb-0' style={{ color: ABOUT_COLORS.TEXT_BLACK }}>The story of how we built Moolah Capital from vision to reality</p>
              </div>
            </div>

            <div className="row g-4 justify-content-center">
              {/* First row - 3 cards */}
              <div className="col-md-6 col-lg-4">
                <div className="card h-100" style={{
                  backgroundColor: 'var(--white-10)',
                  border: 'none',
                  borderRadius: 'var(--border-radius-md)',
                  backdropFilter: 'blur(10px)'
                }}>
                  <div className="card-body text-center">
                    <h5 className="card-title mb-3" style={{
                      color: ABOUT_COLORS.TEXT_BLACK,
                      fontSize: '1.2rem',
                      fontWeight: '600'
                    }}>
                      In the beginning
                    </h5>
                    <p className="card-text" style={{ color: ABOUT_COLORS.TEXT_BLACK, fontSize: '0.9rem' }}>
                      Moolah Capital was founded by two finance sector veterans who combined their expertise in business, technology, and analytics to tackle the challenges of crypto investing. With backgrounds in traditional finance, including stints at Barclays Investment Bank, Bank of America, and Lloyds Bank, they were well-equipped to analyze the crypto market.
                    </p>
                  </div>
                </div>
              </div>

              <div className="col-md-6 col-lg-4">
                <div className="card h-100" style={{
                  backgroundColor: 'var(--white-10)',
                  border: 'none',
                  borderRadius: 'var(--border-radius-md)',
                  backdropFilter: 'blur(10px)'
                }}>
                  <div className="card-body text-center">
                    <h5 className="card-title mb-3" style={{
                      color: ABOUT_COLORS.TEXT_BLACK,
                      fontSize: '1.2rem',
                      fontWeight: '600'
                    }}>
                      Identifying The Problem
                    </h5>
                    <p className="card-text" style={{ color: ABOUT_COLORS.TEXT_BLACK, fontSize: '0.9rem' }}>
                      The team recognized that crypto investing was fraught with risks, including volatile markets, untrustworthy advisors, and confusing marketing. The steep learning curve required to keep up with new coins and protocols made it difficult for most people to invest in crypto.
                    </p>
                  </div>
                </div>
              </div>

              <div className="col-md-6 col-lg-4">
                <div className="card h-100" style={{
                  backgroundColor: 'var(--white-10)',
                  border: 'none',
                  borderRadius: 'var(--border-radius-md)',
                  backdropFilter: 'blur(10px)'
                }}>
                  <div className="card-body text-center">
                    <h5 className="card-title mb-3" style={{
                      color: ABOUT_COLORS.TEXT_BLACK,
                      fontSize: '1.2rem',
                      fontWeight: '600'
                    }}>
                      A New Approach
                    </h5>
                    <p className="card-text" style={{ color: ABOUT_COLORS.TEXT_BLACK, fontSize: '0.9rem' }}>
                      Moolah team applied standard techniques from equity, forex, and rates markets to investigate the crypto market. They analyzed multiple coins, verifying their characteristics, such as volatility, liquidity, correlation to other assets, and historical duration. This fundamental analysis led to the selection of a candidate set of coins that generated stable returns with a good risk trade-off.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="row g-4 justify-content-center mt-4">
              {/* Second row - 3 cards */}
              <div className="col-md-6 col-lg-4">
                <div className="card h-100" style={{
                  backgroundColor: 'var(--white-10)',
                  border: 'none',
                  borderRadius: 'var(--border-radius-md)',
                  backdropFilter: 'blur(10px)'
                }}>
                  <div className="card-body text-center">
                    <h5 className="card-title mb-3" style={{
                      color: ABOUT_COLORS.TEXT_BLACK,
                      fontSize: '1.2rem',
                      fontWeight: '600'
                    }}>
                      Developing Trading Signals
                    </h5>
                    <p className="card-text" style={{ color: ABOUT_COLORS.TEXT_BLACK, fontSize: '0.9rem' }}>
                      Moolah team applied standard techniques from equity, forex, and rates markets to investigate the crypto market. They analyzed multiple coins, verifying their characteristics, such as volatility, liquidity, correlation to other assets, and historical duration. This fundamental analysis led to the selection of a candidate set of coins that generated stable returns with a good risk trade-off.
                    </p>
                  </div>
                </div>
              </div>

              <div className="col-md-6 col-lg-4">
                <div className="card h-100" style={{
                  backgroundColor: 'var(--white-10)',
                  border: 'none',
                  borderRadius: 'var(--border-radius-md)',
                  backdropFilter: 'blur(10px)'
                }}>
                  <div className="card-body text-center">
                    <h5 className="card-title mb-3" style={{
                      color: ABOUT_COLORS.TEXT_BLACK,
                      fontSize: '1.2rem',
                      fontWeight: '600'
                    }}>
                      Continuous Learning
                    </h5>
                    <p className="card-text" style={{ color: ABOUT_COLORS.TEXT_BLACK, fontSize: '0.9rem' }}>
                      Moolah team applied standard techniques from equity, forex, and rates markets to investigate the crypto market. They analyzed multiple coins, verifying their characteristics, such as volatility, liquidity, correlation to other assets, and historical duration. This fundamental analysis led to the selection of a candidate set of coins that generated stable returns with a good risk trade-off.
                    </p>
                  </div>
                </div>
              </div>

              <div className="col-md-6 col-lg-4">
                <div className="card h-100" style={{
                  backgroundColor: 'var(--white-10)',
                  border: 'none',
                  borderRadius: 'var(--border-radius-md)',
                  backdropFilter: 'blur(10px)'
                }}>
                  <div className="card-body text-center">
                    <h5 className="card-title mb-3" style={{
                      color: ABOUT_COLORS.TEXT_BLACK,
                      fontSize: '1.2rem',
                      fontWeight: '600'
                    }}>
                      The Future of Crypto Investing
                    </h5>
                    <p className="card-text" style={{ color: ABOUT_COLORS.TEXT_BLACK, fontSize: '0.9rem' }}>
                      Moolah Capital plans to offer a core set of funds under three thematic approaches: Passive, Smart Beta, and Special Situations. The team continues to search for new fund ideas, evaluating them using the same rigorous methodology. Follow Moolah Capital's News page and social media channels for updates on their journey to revolutionize crypto investing.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Anchoring Convictions Header - Dark navy background */}
        <section className="full-width-dark-section" style={{ backgroundColor: ABOUT_COLORS.DARK_BLUE, paddingTop: '60px', paddingBottom: '30px', position: 'relative' }}>
          {/* Floating bubbles for Anchoring Convictions section */}
          <div className="fx-bubbles" aria-hidden="true" style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 0,
            pointerEvents: 'none'
          }}>
            <span className="b"></span>
            <span className="b"></span>
            <span className="b"></span>
            <span className="b"></span>
            <span className="b"></span>
          </div>

          <div className="container" style={{ color: 'var(--text-white)', paddingLeft: 'var(--container-padding-x)', paddingRight: 'var(--container-padding-x)', position: 'relative', zIndex: 1 }}>
            <div>
              <h2 className="h2 text-center mb-4">Anchoring Convictions</h2>
              <p className='body-text text-center mb-0'>The core beliefs that guide our investment philosophy</p>
            </div>
          </div>
        </section>

        {/* Conviction Sections - With floating bubbles on medium blue sections */}
        {convictionList.map((service, index) => (
          <section
            key={service.id}
            className={index % 2 === 0 ? 'full-width-medium-section' : 'full-width-dark-section'}
            style={{
              backgroundColor: index % 2 === 0 ? ABOUT_COLORS.MEDIUM_BLUE : ABOUT_COLORS.DARK_BLUE,
              minHeight: '500px',
              paddingTop: '60px',
              paddingBottom: '60px',
              display: 'flex',
              alignItems: 'center',
              position: 'relative'
            }}
          >
            {/* Add floating bubbles to all sections - both medium blue and dark navy */}
            <div className="fx-bubbles" aria-hidden="true" style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 0,
              pointerEvents: 'none'
            }}>
              <span className="b"></span>
              <span className="b"></span>
              <span className="b"></span>
              <span className="b"></span>
              <span className="b"></span>
            </div>
            <div className="container" style={{ color: index % 2 === 0 ? 'var(--text-medium-blue)' : 'var(--text-white)', paddingLeft: 'var(--container-padding-x-large)', paddingRight: 'var(--container-padding-x-large)', position: 'relative', zIndex: 1 }}>
              <div
                ref={convictionRefs[index]}
                className={`row align-items-center fade-in-section ${convictionVisible[index] ? 'is-visible' : ''} ${index % 2 !== 0 ? 'flex-column flex-md-row' : 'flex-md-row-reverse flex-column'}`}
              >
                <div className="col-md-6 my-3 d-flex align-items-center justify-content-center">
                  <div className="w-100 px-4">
                    <h3 className="h2 text-start mb-4 fw-bold" style={{ color: index % 2 === 0 ? 'var(--text-medium-blue)' : 'var(--text-white)' }}>
                      {service.title}
                    </h3>
                    <p className="body-text text-start" style={{ color: index % 2 === 0 ? 'var(--text-medium-blue)' : 'var(--text-white)' }}>{service.description}</p>
                  </div>
                </div>
                <div className="col-md-6 my-3 d-flex align-items-center justify-content-center">
                  <div className="w-100 px-4">
                    <img
                      src={service.img}
                      alt={service.title}
                      className="img-fluid rounded no-hover"
                      style={{ maxHeight: '400px', width: '100%', objectFit: 'cover' }}
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = "/placeholder.jpg";
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </section>
        ))}

        <section id="about-cta" className="full-width-medium-section" style={{
          backgroundColor: ABOUT_COLORS.MEDIUM_BLUE,
          paddingTop: 'var(--section-padding-small)',
          paddingBottom: 'var(--section-padding-small)',
          paddingLeft: '0',
          paddingRight: '0',
          marginBottom: '0'
        }}>
          <div className="container text-center-mbl" style={{ paddingLeft: 'var(--container-padding-x-large)', paddingRight: 'var(--container-padding-x-large)' }}>
            <div className="d-flex flex-row gap-4 justify-content-center" style={{ width: '100%' }}>
              <CTASection
                title="Explore the Funds Matching Your Goals"
                cta="View Funds"
                link="/funds"
                theme="learn"
                style={{ width: '45%', minWidth: '250px' }}
                titleStyle={{ color: 'var(--text-medium-blue)' }}
              />
              <CTASection
                title="Start Your Financial Journey"
                cta="Sign Up"
                link="/signup#account-signup"
                theme="signup"
                style={{ width: '45%', minWidth: '250px' }}
                titleStyle={{ color: 'var(--text-medium-blue)' }}
              />
            </div>
          </div>
        </section>


    </div>
  );
};
export default About;
