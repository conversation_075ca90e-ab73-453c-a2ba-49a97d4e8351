import React, { useState } from 'react';
import { useContent } from '../../context/ContentContext';

const ContentManager = () => {
  const { content, updateContent } = useContent();
  const [selectedSection, setSelectedSection] = useState('funds');
  const [editableContent, setEditableContent] = useState(JSON.stringify(content.funds, null, 2));
  const [message, setMessage] = useState({ text: '', type: '' });

  const handleSectionChange = (e) => {
    const section = e.target.value;
    setSelectedSection(section);
    setEditableContent(JSON.stringify(content[section], null, 2));
    setMessage({ text: '', type: '' });
  };

  const handleContentChange = (e) => {
    setEditableContent(e.target.value);
  };

  const handleSave = () => {
    try {
      const parsedContent = JSON.parse(editableContent);
      updateContent(selectedSection, parsedContent);
      setMessage({ text: 'Content updated successfully!', type: 'success' });
    } catch (error) {
      setMessage({ text: `Error: ${error.message}`, type: 'error' });
    }
  };

  return (
    <div className="container py-5">
      <h1 className="display-4 mb-4">Content Manager</h1>
      <p className="body-text mb-4">
        This interface allows you to edit the content displayed on the website. Select a section to edit, make your changes, and click Save.
      </p>

      <div className="row">
        <div className="col-md-3">
          <div className="mb-4">
            <label className="form-label" htmlFor="section">Select Section</label>
            <select 
              className="form-select" 
              id="section" 
              onChange={handleSectionChange} 
              value={selectedSection}
            >
              <option value="funds">Funds</option>
              <option value="navigation">Navigation</option>
              <option value="faq">FAQ</option>
              <option value="about">About</option>
              <option value="strategies">Strategies</option>
            </select>
          </div>

          <button 
            className="btn btn-primary w-100 mb-3" 
            onClick={handleSave}
          >
            Save Changes
          </button>

          {message.text ? <div className={`alert alert-${message.type === 'success' ? 'success' : 'danger'}`}>
              {message.text}
            </div> : null}

          <div className="card mt-4">
            <div className="card-body">
              <h5 className="card-title">Instructions</h5>
              <ul className="card-text">
                <li>Edit the JSON content in the editor</li>
                <li>Ensure valid JSON format</li>
                <li>Click Save to update the content</li>
                <li>Changes will be reflected immediately on the site</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="col-md-9">
          <div className="mb-3">
            <label className="form-label" htmlFor="content-editor">Edit Content (JSON format)</label>
            <textarea
              className="form-control font-monospace"
              id="content-editor"
              onChange={handleContentChange}
              style={{ height: '600px', fontSize: '14px' }}
              value={editableContent}
             />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContentManager;
