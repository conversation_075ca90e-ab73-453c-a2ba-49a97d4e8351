import React, { useState, useEffect } from 'react';

/**
 * A reusable component for full-height hero sections with background image or video
 *
 * @param {Object} props - Component props
 * @param {string} props.backgroundImage - URL of the background image
 * @param {string} props.backgroundVideo - URL of the background video
 * @param {string} props.posterImage - URL of the poster image for video
 * @param {React.ReactNode} props.children - Content to display over the background
 * @param {string} props.overlayColor - CSS color for the overlay (default: rgba(0, 0, 0, 0.4))
 * @param {number} props.videoPlaybackRate - Playback rate for video (default: 1)
 * @returns {JSX.Element}
 */
const FullHeightHero = ({
  backgroundImage,
  backgroundVideo,
  posterImage,
  children,
  overlayColor = 'rgba(0, 0, 0, 0.4)',
  videoPlaybackRate = 1
}) => {
  const [mediaLoaded, setMediaLoaded] = useState(false);
  const videoRef = React.useRef(null);

  // Handle video loading
  useEffect(() => {
    if (!backgroundVideo || !videoRef.current) return;

    const video = videoRef.current;

    const handleCanPlay = () => {
      // Set playback rate
      video.playbackRate = videoPlaybackRate;
      console.log(`Setting video playback rate to ${videoPlaybackRate}`);

      // In test environment, play() might not return a Promise
      try {
        const playPromise = video.play();
        if (playPromise !== undefined) {
          playPromise.catch((err) => {
            console.warn('Autoplay blocked:', err);
          });
        }
      } catch (err) {
        console.warn('Play error:', err);
      }
      setMediaLoaded(true);
    };

    const handleError = (e) => {
      console.error('Video failed to load:', e);
      setMediaLoaded(true); // Still mark as loaded to remove loading state
    };

    const handleMetadataLoaded = () => {
      // Set playback rate when metadata is loaded
      video.playbackRate = videoPlaybackRate;
      console.log(`Metadata loaded - setting video playback rate to ${videoPlaybackRate}`);
    };

    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('error', handleError);
    video.addEventListener('loadedmetadata', handleMetadataLoaded);

    // Try to manually trigger play and set playback rate
    setTimeout(() => {
      if (video && !mediaLoaded) {
        console.log("Attempting manual play");
        // Ensure playback rate is set even if canplay event hasn't fired
        video.playbackRate = videoPlaybackRate;
        
        // In test environment, play() might not return a Promise
        try {
          const playPromise = video.play();
          if (playPromise !== undefined) {
            playPromise.catch(err => console.warn("Manual play failed:", err));
          }
        } catch (err) {
          console.warn('Manual play error:', err);
        }
      }
    }, 1000);

    return () => {
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('error', handleError);
      video.removeEventListener('loadedmetadata', handleMetadataLoaded);
    };
  }, [backgroundVideo, mediaLoaded, videoPlaybackRate]);

  // Handle image loading
  useEffect(() => {
    if (!backgroundImage || backgroundVideo) return;

    const img = new Image();
    img.onload = () => setMediaLoaded(true);
    img.onerror = () => {
      setMediaLoaded(true);
    };
    img.src = backgroundImage;

    // If the image is already in browser cache, onload might not fire
    if (img.complete) {
      setMediaLoaded(true);
    }
  }, [backgroundImage, backgroundVideo]);

  return (
    <section className="full-height-hero" style={{ height: '100vh', minHeight: '100vh', position: 'relative' }}>
      {/* Loading placeholder */}
      {!mediaLoaded && (
        <div
          className="hero-placeholder"
          style={{
            backgroundImage: `url(${posterImage || '/placeholder.jpg'})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            height: '100vh'
          }}
        >
          <div className="spinner-border text-light" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      )}

      {/* Background overlay */}
      <div
        className="hero-overlay"
        style={{
          backgroundColor: overlayColor,
          opacity: mediaLoaded ? 1 : 0
        }}
      />

      {/* Video background */}
      {backgroundVideo && (
        <video
          autoPlay
          className="hero-background-video"
          loop
          muted
          playsInline
          poster={posterImage || '/placeholder.jpg'}
          preload="auto"
          ref={videoRef}
          src={backgroundVideo}
          style={{
            visibility: mediaLoaded ? 'visible' : 'hidden',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100vh',
            objectFit: 'cover',
            zIndex: 0
          }}
        />
      )}

      {/* Image background */}
      {backgroundImage && !backgroundVideo && (
        <div
          className="hero-background-image"
          style={{
            backgroundImage: `url(${backgroundImage})`,
            opacity: mediaLoaded ? 1 : 0,
            height: '100vh',
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}
        />
      )}

      {/* Content */}
      <div className="hero-content">
        {children}
      </div>

      {/* Scroll indicator */}
      <div className="scroll-indicator">
        <i className="bi bi-chevron-double-down"></i>
        <span>Scroll Down</span>
      </div>
    </section>
  );
};

export default FullHeightHero;
