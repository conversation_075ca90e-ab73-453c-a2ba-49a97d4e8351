.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--medium-blue); /* Medium blue background with opacity */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.8s ease-out;
  opacity: 1;
}

.loading-overlay.fade-out {
  opacity: 0;
}

.spinner-container {
  text-align: center;
}

.loading-text {
  color: var(--text-white); /* White text color */
  font-size: 1.2rem;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
  color: var(--text-white) !important; /* White spinner color */
}
