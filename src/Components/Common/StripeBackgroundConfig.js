// Stripe Background Configuration
// This file contains presets and utilities for managing Stripe backgrounds across the site

export const STRIPE_PRESETS = {
  // Subtle effects
  SUBTLE: {
    opacity: 0.3,
    animationDuration: "40s",
    className: "stripe-bg-variant-1"
  },
  
  // Medium effects  
  MEDIUM: {
    opacity: 0.6,
    animationDuration: "30s",
    className: "stripe-bg-variant-2"
  },
  
  // Bold effects
  BOLD: {
    opacity: 0.8,
    animationDuration: "20s",
    className: "stripe-bg-variant-3"
  },
  
  // Custom page presets
  HOMEPAGE: {
    opacity: 0.8,
    animationDuration: "35s",
    // Will cycle through variants automatically
  },
  
  ABOUT: {
    opacity: 0.4,
    animationDuration: "45s",
    className: "stripe-bg-variant-1" // Corner triangles
  },
  
  FUNDS: {
    opacity: 0.6,
    animationDuration: "30s",
    className: "stripe-bg-variant-2" // Side waves
  },
  
  STRATEGIES: {
    opacity: 0.7,
    animationDuration: "25s",
    className: "stripe-bg-variant-3" // Diagonal stripes
  }
};

// Helper function to get variant class based on index
export const getVariantClass = (index, totalVariants = 4) => {
  return `stripe-bg-variant-${(index % totalVariants) + 1}`;
};

// Helper function to apply preset
export const applyPreset = (presetName, customProps = {}) => {
  const preset = STRIPE_PRESETS[presetName] || STRIPE_PRESETS.MEDIUM;
  return {
    enabled: true,
    height: "100%",
    colors: ['var(--button-red)', 'var(--frame-color)'], // Use global colors
    ...preset,
    ...customProps // Allow overrides
  };
};

// Quick disable function
export const disableStripeBackground = () => ({
  enabled: false
});

export default STRIPE_PRESETS;
