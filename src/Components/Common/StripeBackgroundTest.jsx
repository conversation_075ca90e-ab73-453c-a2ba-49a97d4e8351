import React from 'react';
import StripeLikeBackground from './StripeLikeBackground.jsx';

const StripeBackgroundTest = () => {
  return (
    <div style={{ 
      position: 'relative', 
      height: '100vh', 
      backgroundColor: '#f0f0f0',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      {/* Stripe Background Effect */}
      <StripeLikeBackground
        enabled={true}
        height="100vh"
        opacity={0.8}
        colors={['#635bff', '#00ffc8', '#8B1E2D']}
        animationDuration="10s"
      />
      
      {/* Test Content */}
      <div style={{
        position: 'relative',
        zIndex: 10,
        textAlign: 'center',
        color: 'white',
        backgroundColor: 'rgba(0,0,0,0.5)',
        padding: '2rem',
        borderRadius: '10px'
      }}>
        <h1>Stripe Background Test</h1>
        <p>If you can see animated gradients behind this text, the component is working!</p>
        <p>Colors: <PERSON> (#635bff), <PERSON><PERSON> (#00ffc8), <PERSON> (#8B1E2D)</p>
      </div>
    </div>
  );
};

export default StripeBackgroundTest;
