/* Stripe-like Background Effect - Isolated Component */
/* This CSS is scoped to avoid conflicts with existing styles */

.stripe-like-bg-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--stripe-height, 60vh);
  z-index: 1;
  pointer-events: none;
  overflow: hidden;
}

.stripe-like-bg-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform: skewY(var(--stripe-skew, -12deg));
  
  /* Gradient background using CSS variables */
  background: 
    radial-gradient(
      at var(--stripe-pos1, 50% 50%), 
      var(--stripe-color1, #635bff) 0%, 
      transparent var(--stripe-spread1, 50%)
    ),
    radial-gradient(
      at var(--stripe-pos2, 30% 80%), 
      var(--stripe-color2, #8B1E2D) 0%,
      transparent var(--stripe-spread2, 60%)
    ),
    radial-gradient(
      at var(--stripe-pos3, 80% 20%), 
      var(--stripe-color3, #8B1E2D) 0%, 
      transparent var(--stripe-spread3, 40%)
    );
  
  background-size: 200% 200%;
  opacity: var(--stripe-opacity, 0.7);
  animation: stripeFlow var(--stripe-duration, 18s) ease-in-out infinite alternate;
}

/* Animation keyframes */
@keyframes stripeFlow {
  0% {
    background-position: 0% 0%, 100% 100%, 50% 50%;
    transform: skewY(var(--stripe-skew, -12deg)) scale(1);
  }
  25% {
    background-position: 100% 0%, 0% 100%, 100% 0%;
    transform: skewY(var(--stripe-skew, -12deg)) scale(1.05);
  }
  50% {
    background-position: 100% 100%, 0% 0%, 0% 100%;
    transform: skewY(var(--stripe-skew, -12deg)) scale(1);
  }
  75% {
    background-position: 0% 100%, 100% 0%, 50% 50%;
    transform: skewY(var(--stripe-skew, -12deg)) scale(1.02);
  }
  100% {
    background-position: 0% 0%, 100% 100%, 50% 50%;
    transform: skewY(var(--stripe-skew, -12deg)) scale(1);
  }
}

/* Wave-like flowing animation */
@keyframes stripeVibrate {
  0% {
    background-position: 0% 0%, 100% 100%, 0% 100%, 50% 50%;
    transform: scale(1);
  }
  25% {
    background-position: 30% 20%, 70% 80%, 20% 80%, 60% 40%;
    transform: scale(1.03);
  }
  50% {
    background-position: 60% 40%, 40% 60%, 40% 60%, 40% 60%;
    transform: scale(0.98);
  }
  75% {
    background-position: 80% 60%, 20% 40%, 60% 40%, 30% 70%;
    transform: scale(1.02);
  }
  100% {
    background-position: 100% 80%, 0% 20%, 80% 20%, 50% 50%;
    transform: scale(1);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .stripe-like-bg-container {
    height: var(--stripe-height, 50vh);
  }
  
  .stripe-like-bg-effect {
    transform: skewY(calc(var(--stripe-skew, -12deg) * 0.7));
    background-size: 150% 150%;
  }
}

/* Optional: Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .stripe-like-bg-effect {
    animation: none;
  }
}

/* Utility classes for easy testing */
.stripe-bg-subtle {
  --stripe-opacity: 0.3;
}

.stripe-bg-bold {
  --stripe-opacity: 0.9;
}

.stripe-bg-fast {
  --stripe-duration: 8s;
}

.stripe-bg-slow {
  --stripe-duration: 30s;
}

/* Enhanced version with more prominent gradients */
.stripe-bg-enhanced .stripe-like-bg-effect {
  /* Multiple larger gradients for more coverage while avoiding center */
  background:
    /* Primary gradients - larger and more prominent */
    radial-gradient(
      ellipse 1000px 500px at 5% 15%,
      var(--stripe-color1, #635bff) 0%,
      transparent 60%
    ),
    radial-gradient(
      ellipse 800px 400px at 95% 85%,
      var(--stripe-color2, #8B1E2D) 0%,
      transparent 55%
    ),
    radial-gradient(
      ellipse 700px 350px at 85% 5%,
      var(--stripe-color3, #8B1E2D) 0%,
      transparent 50%
    ),
    /* Secondary gradients for more depth */
    radial-gradient(
      ellipse 600px 300px at 15% 85%,
      var(--stripe-color2, #8B1E2D) 0%,
      transparent 45%
    ),
    radial-gradient(
      ellipse 500px 250px at 75% 50%,
      var(--stripe-color1, #635bff) 0%,
      transparent 35%
    );

  /* Larger background size for more dynamic movement */
  background-size: 180% 180%;
}

/* Vibrating version - colors stay visible and pulse */
.stripe-bg-vibrate .stripe-like-bg-effect {
  /* Fixed positioned gradients that stay visible */
  background:
    /* Primary gradients - always visible */
    radial-gradient(
      ellipse 900px 450px at 8% 18%,
      var(--stripe-color1, #635bff) 0%,
      transparent 55%
    ),
    radial-gradient(
      ellipse 750px 375px at 92% 82%,
      var(--stripe-color2, #8B1E2D) 0%,
      transparent 50%
    ),
    radial-gradient(
      ellipse 650px 325px at 88% 8%,
      var(--stripe-color3, #8B1E2D) 0%,
      transparent 45%
    ),
    /* Secondary gradients for depth */
    radial-gradient(
      ellipse 550px 275px at 12% 88%,
      var(--stripe-color2, #8B1E2D) 0%,
      transparent 40%
    ),
    radial-gradient(
      ellipse 450px 225px at 78% 45%,
      var(--stripe-color1, #635bff) 0%,
      transparent 35%
    );

  /* Gentle background movement with vibration */
  background-size: 140% 140%;

  /* Use vibrating animation with gentle movement */
  animation: stripeVibrate var(--stripe-duration, 35s) ease-in-out infinite;
}

/* Stripe-like flowing colors in the defined region */
.stripe-bg-variant-1 .stripe-like-bg-effect {
  /* Stripe-like floating bubbles that don't vanish */
  background:
    /* Purple bubble - floating, doesn't vanish */
    radial-gradient(circle 300px at 20% 30%,
      color-mix(in srgb, var(--chart-fees) 30%, transparent) 0%,
      color-mix(in srgb, var(--chart-fees) 25%, transparent) 40%,
      color-mix(in srgb, var(--chart-fees) 20%, transparent) 70%,
      color-mix(in srgb, var(--chart-fees) 15%, transparent) 100%
    ),

    /* Red bubble - floating, doesn't vanish */
    radial-gradient(circle 250px at 80% 70%,
      color-mix(in srgb, var(--chart-risk) 28%, transparent) 0%,
      color-mix(in srgb, var(--chart-risk) 22%, transparent) 40%,
      color-mix(in srgb, var(--chart-risk) 18%, transparent) 70%,
      color-mix(in srgb, var(--chart-risk) 12%, transparent) 100%
    ),

    /* Yellow bubble - floating, doesn't vanish */
    radial-gradient(circle 200px at 60% 20%,
      color-mix(in srgb, var(--chart-liquidity) 25%, transparent) 0%,
      color-mix(in srgb, var(--chart-liquidity) 20%, transparent) 40%,
      color-mix(in srgb, var(--chart-liquidity) 15%, transparent) 70%,
      color-mix(in srgb, var(--chart-liquidity) 10%, transparent) 100%
    );

  /* Extend region to left margin */
  clip-path: polygon(
    0% 20%,     /* Extend to left margin, top */
    100% 15%,   /* Keep original right edge at top right corner */
    100% 85%,   /* Keep original right edge at bottom right corner */
    0% 80%      /* Extend to left margin, bottom */
  );

  background-size: 300% 200%;
  animation: stripeVibrate 8s ease-in-out infinite;
}

/* Variant 2: GenAI Funds - Regular, nice geometric pattern */
.stripe-bg-variant-2 .stripe-like-bg-effect {
  /* Same floating bubbles as Variant 1 */
  background:
    /* Purple bubble - floating, doesn't vanish */
    radial-gradient(circle 300px at 20% 30%,
      color-mix(in srgb, var(--chart-fees) 30%, transparent) 0%,
      color-mix(in srgb, var(--chart-fees) 25%, transparent) 40%,
      color-mix(in srgb, var(--chart-fees) 20%, transparent) 70%,
      color-mix(in srgb, var(--chart-fees) 15%, transparent) 100%
    ),

    /* Red bubble - floating, doesn't vanish */
    radial-gradient(circle 250px at 80% 70%,
      color-mix(in srgb, var(--chart-risk) 28%, transparent) 0%,
      color-mix(in srgb, var(--chart-risk) 22%, transparent) 40%,
      color-mix(in srgb, var(--chart-risk) 18%, transparent) 70%,
      color-mix(in srgb, var(--chart-risk) 12%, transparent) 100%
    ),

    /* Yellow bubble - floating, doesn't vanish */
    radial-gradient(circle 200px at 60% 20%,
      color-mix(in srgb, var(--chart-liquidity) 25%, transparent) 0%,
      color-mix(in srgb, var(--chart-liquidity) 20%, transparent) 40%,
      color-mix(in srgb, var(--chart-liquidity) 15%, transparent) 70%,
      color-mix(in srgb, var(--chart-liquidity) 10%, transparent) 100%
    ),

    /* Very light overlay in center for text readability */
    radial-gradient(ellipse 400px 200px at 50% 50%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      transparent 80%
    );

  clip-path: polygon(
    0% 20%, 100% 15%, 100% 85%, 0% 80%
  );

  background-size: 300% 200%;
  animation: stripeVibrate 8s ease-in-out infinite;
}

/* Variant 3: DeFi Leaders Fund - Clean, balanced geometric pattern */
.stripe-bg-variant-3 .stripe-like-bg-effect {
  /* Same floating bubbles as Variant 1 */
  background:
    /* Purple bubble - floating, doesn't vanish */
    radial-gradient(circle 300px at 20% 30%,
      color-mix(in srgb, var(--chart-fees) 30%, transparent) 0%,
      color-mix(in srgb, var(--chart-fees) 25%, transparent) 40%,
      color-mix(in srgb, var(--chart-fees) 20%, transparent) 70%,
      color-mix(in srgb, var(--chart-fees) 15%, transparent) 100%
    ),

    /* Red bubble - floating, doesn't vanish */
    radial-gradient(circle 250px at 80% 70%,
      color-mix(in srgb, var(--chart-risk) 28%, transparent) 0%,
      color-mix(in srgb, var(--chart-risk) 22%, transparent) 40%,
      color-mix(in srgb, var(--chart-risk) 18%, transparent) 70%,
      color-mix(in srgb, var(--chart-risk) 12%, transparent) 100%
    ),

    /* Yellow bubble - floating, doesn't vanish */
    radial-gradient(circle 200px at 60% 20%,
      color-mix(in srgb, var(--chart-liquidity) 25%, transparent) 0%,
      color-mix(in srgb, var(--chart-liquidity) 20%, transparent) 40%,
      color-mix(in srgb, var(--chart-liquidity) 15%, transparent) 70%,
      color-mix(in srgb, var(--chart-liquidity) 10%, transparent) 100%
    ),

    /* Very light overlay in center for text readability */
    radial-gradient(ellipse 400px 200px at 50% 50%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      transparent 80%
    );

  clip-path: polygon(
    0% 20%, 100% 15%, 100% 85%, 0% 80%
  );

  background-size: 300% 200%;
  animation: stripeVibrate 8s ease-in-out infinite;
}



/* Variant 4: Abstract Orbs - Multiple organic, orb-like shapes */
.stripe-bg-variant-4 .stripe-like-bg-effect {
  background:
    /* Large organic orb */
    radial-gradient(circle at 25% 40%, var(--chart-liquidity, #ffd93d) 0%, rgba(255, 217, 61, 0.7) 4%, rgba(255, 217, 61, 0.4) 12%, rgba(255, 217, 61, 0.15) 25%, transparent 45%),
    radial-gradient(circle at 20% 45%, var(--chart-liquidity, #ffd93d) 0%, rgba(255, 217, 61, 0.5) 8%, rgba(255, 217, 61, 0.2) 18%, transparent 35%),
    /* Medium orb */
    radial-gradient(circle at 80% 60%, var(--button-red, #635bff) 0%, rgba(99, 91, 255, 0.6) 6%, rgba(99, 91, 255, 0.3) 16%, transparent 40%),
    radial-gradient(circle at 75% 65%, var(--button-red, #635bff) 0%, rgba(99, 91, 255, 0.4) 10%, transparent 25%),
    /* Small accent orbs */
    radial-gradient(circle at 50% 85%, var(--chart-liquidity, #ffd93d) 0%, rgba(255, 217, 61, 0.4) 12%, transparent 30%),
    radial-gradient(circle at 15% 15%, var(--button-red, #635bff) 0%, rgba(99, 91, 255, 0.3) 15%, transparent 35%);
  background-size: 170% 150%;
  animation: stripeFlow var(--stripe-duration, 28s) ease-in-out infinite alternate;
}

/* New Floating Bubbles Effect */
.fx-bubbles {
  position: absolute;
  inset: 0;
  pointer-events: none;
  overflow: hidden;
  z-index: 1;
}

.fx-bubbles .b {
  position: absolute;
  width: 36vmax;
  height: 36vmax;
  border-radius: 50%;
  background: radial-gradient(closest-side, var(--color) 0%, rgba(0,0,0,0) 70%);
  mix-blend-mode: screen;
  filter: blur(60px) saturate(130%);
  opacity: 0.8;
  transform: translate3d(var(--x), var(--y), 0) scale(var(--s));
  animation: float var(--dur) var(--delay, 0s) infinite alternate ease-in-out;
}

.fx-bubbles .b:nth-child(1) {
  --color: rgba(from var(--bubble-blue) r g b / 0.70);
  --x: -10vw; --y: -15vh; --s: 1.05;
  --dx: 16vw; --dy: 12vh; --dur: 10s; --delay: 0s;
}
.fx-bubbles .b:nth-child(2) {
  --color: rgba(from var(--bubble-cyan) r g b / 0.55);
  --x: 15vw; --y: -20vh; --s: 1.15;
  --dx: -12vw; --dy: 18vh; --dur: 10s; --delay: -2s;
}
.fx-bubbles .b:nth-child(3) {
  --color: rgba(from var(--bubble-purple) r g b / 0.60);
  --x: 50vw; --y: -10vh; --s: 1.10;
  --dx: -8vw; --dy: 14vh; --dur: 10s; --delay: -4s;
}
.fx-bubbles .b:nth-child(4) {
  --color: rgba(from var(--bubble-green) r g b / 0.50);
  --x: 70vw; --y: 10vh; --s: 0.95;
  --dx: -14vw; --dy: -6vh; --dur: 10s; --delay: -6s;
}
.fx-bubbles .b:nth-child(5) {
  --color: rgba(from var(--bubble-red) r g b / 0.45);
  --x: 10vw; --y: 30vh; --s: 1.25;
  --dx: 12vw; --dy: -10vh; --dur: 10s; --delay: -8s;
}

@keyframes float {
  0% { transform: translate3d(var(--x), var(--y), 0) scale(var(--s)); }
  100% { transform: translate3d(calc(var(--x) + var(--dx)), calc(var(--y) + var(--dy)), 0) scale(calc(var(--s) * 1.06)); }
}

@media (prefers-reduced-motion: reduce) {
  .fx-bubbles .b { animation: none; }
}
