import React, { useEffect, useState } from 'react';
import './StripeLikeBackground.css';

const StripeLikeBackground = ({ 
  enabled = true,
  height = '60vh',
  skewAngle = '-12deg',
  opacity = 0.7,
  animationDuration = '18s',
  colors = ['#635bff', '#8B1E2D', '#ffd93d'],
  className = ''
}) => {
  const [cssVars, setCssVars] = useState({});

  useEffect(() => {
    if (!enabled) return;

    // Generate random positions and spreads for the gradients
    const generateRandomValues = () => {
      return {
        '--stripe-pos1': `${Math.random() * 100}% ${Math.random() * 100}%`,
        '--stripe-pos2': `${Math.random() * 100}% ${Math.random() * 100}%`,
        '--stripe-pos3': `${Math.random() * 100}% ${Math.random() * 100}%`,
        '--stripe-color1': colors[0] || '#635bff',
        '--stripe-color2': colors[1] || '#8B1E2D',
        '--stripe-color3': colors[2] || '#ffd93d',
        '--stripe-spread1': `${30 + Math.random() * 40}%`,
        '--stripe-spread2': `${30 + Math.random() * 40}%`,
        '--stripe-spread3': `${30 + Math.random() * 40}%`,
        '--stripe-height': height,
        '--stripe-skew': skewAngle,
        '--stripe-opacity': opacity,
        '--stripe-duration': animationDuration
      };
    };

    setCssVars(generateRandomValues());

    // Optional: Update positions periodically for more dynamic effect
    const interval = setInterval(() => {
      setCssVars(generateRandomValues());
    }, 20000); // Update every 20 seconds

    return () => clearInterval(interval);
  }, [enabled, height, skewAngle, opacity, animationDuration, colors]);

  if (!enabled) return null;

  return (
    <div
      className={`stripe-like-bg-container ${className}`}
      style={cssVars}
    >
      <div className="stripe-like-bg-effect" />
    </div>
  );
};

export default StripeLikeBackground;
