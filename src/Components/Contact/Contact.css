/* === Hero Section === */
.contact-hero {
  position: relative;
  background: linear-gradient(135deg, #0e101c, #1c1f2b);
  padding: 80px 0;
  color: white;
  overflow: hidden;
}

.particle-canvas {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
  height: 300px;
}

.contact-hero-content {
  position: relative;
  z-index: 1;
  text-align: center;
}

.hero-title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #ccc;
}

.hero-decoration {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 1.5rem;
  color: #ffcc00;
}

/* === Contact Form Section === */
.contact-form-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 600px;
  margin: 0 auto;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.card-header {
  text-align: center;
  margin-bottom: 2rem;
}

.card-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #888;
}

.input-container {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 1rem;
  outline: none;
  transition: border 0.3s ease;
}

.form-input:focus {
  border-color: var(--primary-color);
}

label {
  left: 40px;
  top: 12px;
  color: #999;
  font-size: 0.9rem;
  pointer-events: none;
  transition: all 0.2s ease;
}

label.active,
.form-input:focus + label {
  top: -10px;
  font-size: 0.75rem;
  color: var(--primary-color);
}

.input-highlight {
  height: 2px;
  background: var(--primary-color);
  width: 0;
  transition: width 0.3s ease;
}

.form-input:focus ~ .input-highlight {
  width: 100%;
}

/* Simple checkbox styling - completely new implementation */
.simple-checkbox-container {
  display: flex;
  align-items: center;
  justify-content: center; /* Center the container */
  margin: 10px 0 15px;
  width: 100%; /* Ensure container takes full width */
}

.checkbox-center-wrapper {
  display: inline-flex;
  align-items: right;
  gap: 5px; /* Space between checkbox and label */
}

/* Adjust button group spacing */
.form-group.button-group {
  margin-top: 10px; /* Reduce from default spacing */
  display: flex;
  justify-content: center;
  align-items: center;
}

/* === Status Message === */
.status-message {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
  padding: 0.8rem 1rem;
  border-radius: 6px;
  font-size: 0.95rem;
}

.status-message.success {
  background: #e6f9ec;
  color: #2e7d32;
}

.status-message.error {
  background: #fdecea;
  color: #c62828;
}

/* === Submit Button === */
.submit-button {
  background: var(--button-red);
  color: var(--button-text);
  padding: 6px 16px !important;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  min-width: 120px !important;
  width: auto !important;
  display: inline-flex !important;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 12px var(--button-shadow);
  font-weight: 500;
  letter-spacing: 0.5px;
  font-size: 0.9rem;
  margin: 0 auto !important;
}

.submit-button:hover:not(:disabled) {
  background: var(--button-red);
  transform: scale(1.05);
  box-shadow: 0 6px 18px var(--button-shadow);
}

.submit-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1rem;
  width: 100%;
}

.button-text {
  display: inline-block;
  text-align: center;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #fff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* === Container === */
.contact-container {
  padding: 0;
  margin: 0;
  width: 100%;
}

.page-content-container {
  padding: 4rem 1rem;
}

/* === Responsive Styles === */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .contact-form-card {
    padding: 1.5rem;
  }

  .form-input {
    font-size: 0.95rem;
  }

  .submit-button {
    padding: 0.65rem 1.2rem;
    font-size: 0.95rem;
    width: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .button-content {
    justify-content: center;
    text-align: center;
    width: 100%;
  }

  .button-content span {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 1.5rem;
  }

  .hero-subtitle {
    font-size: 0.95rem;
  }

  .hero-decoration i,
  .card-icon i {
    font-size: 1.5rem;
  }

  .contact-container {
    padding: 0;
    margin: 0;
    width: 100%;
  }

  .checkbox-text {
    font-size: 0.9rem;
  }
}

/* === Additional Styling Enhancements === */
.contact-form-card .card-header h2,
.contact-form-card .card-header p,
.contact-info-card .card-header h2,
.contact-info-card .card-header p {
  color: #000000;
}

.card-icon {
  color: var(--button-red);
}

label.active,
.form-input:focus + label {
  color: var(--button-red);
  background-color: white;
  padding: 0 5px;
}

.form-group label {
  color: #555;
}

.info-content h3,
.social-media h3 {
  color: #000000;
}

.info-details p,
.info-details address {
  color: #333333;
}

.button-group {
  margin-top: 20px;
}

.simple-checkbox-container {
  display: flex;
  align-items: center;
}

.checkbox-center-wrapper {
  display: flex;
  align-items: center;
  gap: 10px; /* Space between checkbox and label */
}

.simple-checkbox-input {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--primary); /* Modern browsers for custom color */
}

.simple-checkbox-label {
  font-size: 0.80rem;
  color: #333;
  cursor: pointer;
}

