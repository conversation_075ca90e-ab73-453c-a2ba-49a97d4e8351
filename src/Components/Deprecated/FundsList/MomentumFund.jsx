import React from 'react';
import PortfolioTable from '../../Global/table/PortfolioTable';
import CTASection from '../../../App/CTA/CTASection';

const MomentumFund = () => {
	return (
		<div className='text-black'>
			<div className='mb-5'>
				<div className='container py-lg-5 py-md-5 py-sm-2 py-2'>
					<div className='help-text mb-4 d-flex align-items-center gap-3'>
						<div className='line ms-4' />
						<p className='h2'>AlphaGlobal Momentum Fund </p>
					</div>
					<div className='row body-text text-left border-bottom pb-3'>
						<div className='col-lg-5 my-3 body-text'>
							<div className='body-text text-left ms-4'>
								The AlphaGlobal Momentum Fund uses a dynamic momentum trading strategy to capture gains in excess of market alpha. By leveraging advanced analytics and disciplined risk management, the fund identifies and exploits short-term trends across the crypto market, actively adjusting positions to achieve superior returns while controlling volatility.
							</div>
							<p/>
							<div className='body-text text-left ms-4'>
								All coins have passed minimum Moolah criteria for security, liquidity, and regulatory oversight (where appropriate), ensuring that investor funds are safeguarded at every step. We emphasize a conservative approach to capital preservation by incorporating stable coins, crypto derivatives, and assets with proven track records into our investment strategy. This focus on capital protection allows us to mitigate risk while still capturing the upside potential of the crypto market.
							</div>
							<p/>
							<div className='body-text text-left ms-4'>
								In addition, a comprehensive process of portfolio analysis and performance monitoring is applied to ensure continuous, reliable price availability from trusted sources before enabling live trading of the Momentum Fund. This dynamic monitoring system not only tracks market fluctuations in real time but also reinforces our commitment to a protective and conservative investment approach. By carefully balancing risk and reward, we ensure that our clients’ capital is consistently managed with a focus on long-term security and stability.
							</div>
						</div>
						<div className='col-lg-1' />
						<div className='col-lg-6 my-3'>
							<div className='row body-text border-bottom pb-3'>
								<div className='col-lg-3 h4 fw-bold pb-lg-0 pb-md-0 pb-sm-3 pb-3'>
									Investment Objective
								</div>
								<div className='col-lg-9body-text ps-lg-5 ps-md-5 ps-sm-2 ps-4'>
									The AlphaGlobal Momentum fund aims to generate alpha by dynamically trading crypto assets through a momentum strategy. It leverages advanced technical analysis and strict risk management to identify short-term trends, capturing excess returns while adapting to volatility and preserving capital during downturns.
								</div>
							</div>
							<div className='row body-text border-bottom pb-3'>
								<div className='col-lg-3 h4 fw-bold pb-lg-0 pb-md-0 pb-sm-3 pb-3'>
									NAV as of 31/3/2025
								</div>
								<div className='col-lg-9body-text ps-lg-5 ps-md-5 ps-sm-2 ps-4'>
									USD$28.278
								</div>
							</div>
							<div className='row body-text border-bottom pb-3'>
								<div className='col-lg-3 h4 fw-bold pb-lg-0 pb-md-0 pb-sm-3 pb-3'>
									Fees
								</div>
								<div className='col-lg-9body-text ps-lg-5 ps-md-5 ps-sm-2 ps-4'>
									Management Fee: 0.25% on Funds Invested. Performance Fee: 12.5% on Gains.
								</div>
							</div>
							<div className='row body-text border-bottom pb-3'>
								<div className='col-lg-3 h4 fw-bold pb-lg-0 pb-md-0 pb-sm-3 pb-3'>
									Size
								</div>
								<div className='col-lg-9body-text ps-lg-5 ps-md-5 ps-sm-2 ps-4'>
									Minimum fund size is USD10.00M. Current Size is $8,028,000
								</div>
							</div>
							<div className='row body-text border-bottom py-3'>
								<div className='col-lg-3 h4 fw-bold pb-lg-0 pb-md-0 pb-sm-3 pb-3'>
									Diversification
								</div>
								<div className='col-lg-9body-text ps-lg-5 ps-md-5 ps-sm-2 ps-4'>
									<p>
										The AlphaGlobal Momentum Fund will be diversified across Crypto coins with maximum holding of 10% in any single coin or token.
									</p>
									<p>
										Regular re-balancing of the Fund will be done to avoid concentration and liquidity risks for coins comprising more than 10% of fund holdings.
									</p>
								</div>
							</div>
							<div className='row body-text border-bottom pb-3'>
								<div className='col-lg-3 h4 fw-bold pb-lg-0 pb-md-0 pb-sm-3 pb-3'>
									Liquidity
								</div>
								<div className='col-lg-9body-text ps-lg-5 ps-md-5 ps-sm-2 ps-4'>
									Access to minimum 3 trading venues for purchase and sale of AlphaGlobal Momentum Fund coins.
								</div>
							</div>
							<div className='row body-text border-bottom pb-3'>
								<div className='col-lg-3 h4 fw-bold pb-lg-0 pb-md-0 pb-sm-3 pb-3'>
									Risk
								</div>
								<div className='col-lg-9body-text ps-lg-5 ps-md-5 ps-sm-2 ps-4'>
									<p>
										On Risk the funds Sharpe ratio is 5.50%, while Sortino and Kappa-3 are calculated at 3.88% and 3.85% respectively.
									</p>
								</div>
							</div>
							<div className='row body-text py-3'>
								<div className='col-lg-3 h4 fw-bold pb-lg-0 pb-md-0 pb-sm-3 pb-3'>
									Performance
								</div>
								<div className='col-lg-9body-text ps-lg-5 ps-md-5 ps-sm-2 ps-4'>
									<p>
										AlphaGlobal Momentum Fund has consistently delivered strong income performance over the past 12 months by harnessing diverse yield-generating opportunities such as margin lending, staking, token airdrops, and P2P lending. Year-to-date income reflects our disciplined, income-focused strategy, while all-time yields underscore our commitment to steady cash flow.
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/*/////////////////////// Portfolio Table /////////////////////// */}
			<div className='container'>
				<div className='help-text mb-4 d-flex align-items-center gap-3'>
					<div className='line' />
					<h2 className='m-0'>AlphaGlobal Momentum Fund Portfolio</h2>
				</div>

				{/* 
				Key: Use a centered container at 80% width 
				and apply overflow if columns exceed the width.
				*/}
				<div 
				style={{ 
					width: '80%', 
					margin: '0 auto', 
					overflowX: 'auto' 
				}}
				>
				<PortfolioTable
					// Force the table to fill 100% of its parent
					cols={['Name', 'Coin', 'Price', 'Holding', 'Percentage(%)', 'Valuation(USD)']}
					data={[
					{ name: 'Ethereum', coin: 'ETH', price: 3000.0, volume: 828111.11, change: 16.67, market_cap: 2484333.33 },
					{ name: 'Ethena', coin: 'ENA', price: 0.57, volume: 432000000.0, change: 16.67, market_cap: 2484333.33 },
					{ name: 'Polkadot', coin: 'DOT', price: 8.02, volume: 31000000.0, change: 16.67, market_cap: 2484333.33 },
					{ name: 'Ripple', coin: 'XRP', price: 3.28, volume: 75610000.0, change: 16.67, market_cap: 2484333.33 },
					{ name: 'Sui', coin: 'SUI', price: 0.747, volume: 332000000.0, change: 16.67, market_cap: 2484333.33 },
					{ name: 'Tron', coin: 'TRX', price: 1.88, volume: 132000000.0, change: 16.67, market_cap: 2484333.33 }
					]}
					style={{ width: '100%' }}
				/>
				</div>
			</div>

			<CTASection />
		</div>

	);
}
export default MomentumFund;