import React from 'react';
import useAssetCache from '../../context/useAssetCache.js';

const Methodology = () => {

	const img1 = useAssetCache('methodologyImg1', '/iStock-1278916429.jpg');

	return (
		<>
			<div className='red text-white'>
				<div className="d-flex justify-content-center align-items-center overflow-hidden red mx-5 pb-5">
					<div className="cards mt-5 pt-5 pb-5">
						<div className='row'>
							<div className="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12">
								<div className="section_4_part_2 d-lg-flex flex-row mb-5">

									<div className='py-5 p-lg-5'>
										<div className="body-text display-4 fw-bold">Moolah Capital Methodology</div>
										<p className="body-text">
											As a fund management team creating a crypto index, Moolah methodology for the Income fund focuses on selecting stable, liquid coins with sufficient historical data for backtesting. We prioritize assets with high market capitalization and consistent trading volumes to ensure liquidity and smooth execution.
										</p>
										<p className='body-text pb-5'>
											Stablecoins are considered for their volatility-reducing properties, while established cryptocurrencies like Bitcoin and Ethereum provide a solid foundation. We also evaluate technological soundness, regulatory compliance, and community support to mitigate risks. Reliable data sources are crucial for accurate backtesting and performance analysis. Finally, we implement a regular review and rebalancing process to adapt to market shifts and maintain the index's integrity.
										</p>
									</div>

									<img
										alt="Methodology Illustration"
										className="w-mobile-full w-40"
										src={img1}
										onError={(e) => {
											e.target.onerror = null; // Prevent infinite loop
											e.target.src = "/placeholder.jpg";
										}}
									/>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>


			<div className='text-black'>

				<div className='py-5 my-5'>
					<div className='container py-5 my-5'>
						<h1 className='display-4 fw-bold pb-5 body-text'>Moolah Capital Methodology</h1>

						<div className='row body-text'>
							<div className='col-md-4 p-4'>
								<div className='fw-bold mb-2'>
									Passive funds
								</div>
								<p>
									They invest by market indices by buying and selling assets that are included in the target index.
								</p>
							</div>
							<div className='col-md-4 p-4'>
								<div className='fw-bold mb-2'>
									Smart Beta funds
								</div>
								<p>
									They are active funds that have the goal to beat the market (“beta”) by ad-hoc investment strategies.
								</p>
							</div>
							<div className='col-md-4 p-4'>
								<div className='fw-bold mb-2'>
									Special situations funds
								</div>
								<p>
									They active funds are built by professional fund managers around special themes and market segments.
								</p>
							</div>
						</div>
					</div>
				</div>

			</div>
		</>

	);
}

export default Methodology;
