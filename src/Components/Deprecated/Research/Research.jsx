import React, { useState } from 'react';
import useAssetCache from '../../context/useAssetCache.js';

const Research = () => {

	const img1 = useAssetCache('researchImg1', '/iStock-1278916410.webp');

	const [activeIndex1, setActiveIndex1] = useState(null);
	const [activeIndex2, setActiveIndex2] = useState(null);
	const [activeIndex3, setActiveIndex3] = useState(null);
	const toggleAccordion1 = index => {
		setActiveIndex1(activeIndex1 === index ? null : index);
	};

	const toggleAccordion2 = index => {
		setActiveIndex2(activeIndex2 === index ? null : index);
	};

	const toggleAccordion3 = index => {
		setActiveIndex3(activeIndex3 === index ? null : index);
	};

	const faqs = [
		{
			question: 'What are the key components of fund risk management?',
			answer:
				'Key components include market risk, liquidity risk, credit risk, operational risk and legal risk, each requiring specific strategies to mitigate potential losses.',
		},
		{
			question: 'How do fund managers implement risk management strategies?',
			answer:
				'Fund managers implement strategies through diversification, hedging techniques, stress testing and employing sophisticated modelling to forecast potential risks.',
		},
		{
			question: 'What are the best practices for fund risk management?',
			answer:
				'Effective fund risk management practices include diversification of investments, regular stress testing and the use of risk assessment tools to identify potential vulnerabilities. Implementing robust compliance measures and maintaining transparent reporting standards are also crucial for mitigating risks.',
		},
		{
			question: 'How do fund managers assess and mitigate operational risks?',
			answer:
				'Funds assess operational risks by conducting thorough due diligence on service providers, implementing strong internal controls and utilizing technology for real-time monitoring. Regular audits and scenario analysis help in identifying potential operational failures and developing contingency plans.',
		},
		{
			question: 'What are some crypto risks facing fund managers? How can fund managers avoid these risks?',
			answer: `
    <p>Some of the crypto risks facing fund managers include:</p>
    <ol>
      <li>Market Volatility – Crypto assets are highly volatile, leading to significant price swings.</li>
    </ol>
    <p style="margin: -10px 0 20px 15px;font-size:0.8rem;"><em>Mitigation</em>: Use of hedging strategies (options, futures), portfolio diversification, and setting stop-loss limits.</p>
    <ol start="2">
      <li>Regulatory Uncertainty – Evolving regulations across different jurisdictions can impact fund operations.</li>
    </ol>
    <p style="margin: -10px 0 20px 15px;font-size:0.8rem;"><em>Mitigation</em>: Legal and compliance teams tracking regulatory updates, structuring funds in crypto-friendly jurisdictions, and maintaining flexible investment strategies.</p>
    <ol start="3">
      <li>Custody and Security Risks – Cybersecurity threats such as exchange hacks, wallet theft, or private key mismanagement.</li>
    </ol>
    <p style="margin: -10px 0 20px 15px;font-size:0.8rem;"><em>Mitigation</em>: Use of institutional-grade custodians, multi-signature wallets, cold storage, and regular security audits.</p>
    <ol start="4">
      <li>Counterparty Risk – Exposure to third parties such as exchanges, custodians, or counterparties in derivatives trading.</li>
    </ol>
    <p style="margin: -10px 0 20px 15px;font-size:0.8rem;"><em>Mitigation</em>: Conducting thorough due diligence, diversifying counterparties, and using smart contracts with decentralized finance (DeFi) protocols cautiously.</p>
    <ol start="5">
      <li>Smart Contract Risks – Exploits, bugs, or vulnerabilities in smart contracts used in DeFi and on-chain protocols.</li>
    </ol>
    <p style="margin: -10px 0 20px 15px;font-size:0.8rem;"><em>Mitigation</em>: Using audited protocols, limiting exposure to experimental contracts, and continuously monitoring for security updates.</p>
    <ol start="6">
      <li>Operational Risks – Inefficiencies in trading execution, order processing, and compliance failures.</li>
    </ol>
    <p style="margin: -10px 0 20px 15px;font-size:0.8rem;"><em>Mitigation</em>: Automating processes where possible, implementing robust risk management frameworks, and conducting regular stress tests.</p>
    <ol start="8">
      <li>Reputational Risk – Negative news, regulatory crackdowns, or social sentiment shifts affecting market confidence.</li>
    </ol>
    <p style="margin: -10px 0 20px 15px;font-size:0.8rem;"><em>Mitigation</em>: Transparency in fund management, investor education, and proactive communication strategies.</p>
    <p>By employing these risk management measures, Moolah funds customers can navigate the complexities of crypto investments while protecting their portfolios from crypto-related losses.</p>
  `
		}
	];

	const riskManagementContent = [
		{
			title: "Market Risk",
			definition: "Exposure to losses from market price fluctuations (e.g., equities, currencies).",
			management: "Use Value-at-Risk (VaR) models estimate potential losses over a set period. Strict control of leverage is critical to minimize losses."
		},
		{
			title: "Liquidity Risk",
			definition: "Inability to meet obligations for payments and redemptions due to less than perfectly liquid assets.",
			management: "Balance liquid reserves with illiquid investments. Measure and use liquidity metrics such as LCR. Align term structure assets and liabilities. Structure redemption terms and apply fees for requests to redeem funds at short notice."
		},
		{
			title: "Credit Risk",
			definition: "Counterparty default (e.g., derivatives trades).",
			management: "Managed exposures with strict alignment to fund objectives. Verify and risk test counterparties. Measure and managae concentration risks by coin/fund/regulatory regime."
		},
		{
			title: "Operational Risk",
			definition: "Internal failures (systems, fraud, cyberattacks).",
			management: "Robust IT infrastructure. Continuous education and training of staff. Automated compliance tools to ensure practice follows policy. Applied & verified cybersecurity protocols."
		},
		{
			title: "Legal/Regulatory Risk",
			definition: "Penalties from non-compliance e.g. MICA regulations(Europe), AML/KYC (mutiple locations)",
			management: "On-call legal teams, applied regulatory technology (RegTech) and jurisdictional diversification."
		},
	]

	const cardContent = [
		{
			title: "Risk Management Strategies",
			cards: [
				{
					title: "Applied Intelligence",
					description: "Using AI Models, including LLM, to validate investment ideas, check data quality, search for 'black swan' events, ensure regulatory compliance."
				},
				{
					title: "Diversification",
					description: "Spread investments across uncorrelated assets and strategies to reduce concentration risk."
				},
				{
					title: "Hedging",
					description: "Use derivatives to offset losses, such as shorting BTC options/futures to hedge a fund with crypto market exposure."
				},
				{
					title: "Stress Testing & Scenario Analysis",
					description: "Simulate crises to assess portfolio resilience and adjust leverage or liquidity buffers."
				},
				{
					title: "Quantitative Models",
					description: "Machine learning algorithms predict risks using historical data, including Monte Carlo simulations."
				}
			]

		},
		{
			title: "Emerging Trends",
			cards: [
				{
					title: "Regulatory Pressure",
					description: "Post-2008 reforms and growing interest in Crypto markets mandate transparency and stress testing, increasing compliance costs."
				},
				{
					title: "Technology Integration",
					description: "AI/ML for predictive analytics and blockchain for enhanced transparency in OTC derivatives."
				},
				{
					title: "Increasing Security Risks",
					description: "Assess climate risk and social governance using tools like SASB metrics."
				},
				{
					title: "Big Data & Alternative Data",
					description: "Use satellite imagery, social media sentiment, credit card trends, applied usage of Crypto coins to anticipate market shifts."
				}
			]

		}
	]

	const renderCardSections = (content, activeIndex, toggleAccordion) => (
		<div className="accordion" id="accordionFlushExample">
			{content.map((item, index) => (
				<div className="accordion-item" key={index}>
					<div className="accordion-header ">
						<button
							className={`accordion-button dark-2 ${activeIndex === index ? '' : 'collapsed'}`}
							onClick={() => toggleAccordion(index)}
							type="button"
						>
							<span className='h3'>{item.title}</span>
						</button>
					</div>
					<div
						className={`accordion-collapse collapse ${activeIndex === index ? 'show' : ''}`}
					>
						<div className="accordion-body dark-2 lh-base small">
							<div className='row'>
								{
									item?.cards?.map((el, i) => {
										return <div className='col-lg-4 pb-4' key={i}>
											<div className='card black p-4 py-5 h-100 rounded-0'>
												<div className='h4 mb-4 text-red'>
													{el.title}
												</div>
												<div className=''>
													{el.description}
												</div>
											</div>
										</div>
									})
								}
							</div>

						</div>
					</div>
				</div>
			))}
		</div>
	);

	const renderRiskManagement = (content, activeIndex, toggleAccordion) => (
		<div className="accordion" id="accordionFlushExample">
			{content.map((item, index) => (
				<div className="accordion-item" key={index}>
					<div className="accordion-header ">
						<button
							className={`accordion-button dark-2 ${activeIndex === index ? '' : 'collapsed'}`}
							onClick={() => toggleAccordion(index)}
							type="button"
						>
							<span className='h5'>{item.title}</span>
						</button>
					</div>
					<div
						className={`accordion-collapse collapse ${activeIndex === index ? 'show' : ''}`}
					>
						<div className="accordion-body lh-base small">
							<div className='h4 fw-bold'>Definition: </div>
							<div className='mb-4'>
								{item.definition}
							</div>
							<div className='h4 fw-bold'>Management: </div> {item.management}
						</div>
					</div>
				</div>
			))}
		</div>
	);


	const renderAccordion = (faqs, activeIndex, toggleAccordion) => (
		<div className="accordion" id="accordionFlushExample">
			{faqs.map((faq, index) => (
				<div className="accordion-item" key={index}>
					<div className="accordion-header ">
						<button
							className={`accordion-button ${activeIndex === index ? '' : 'collapsed'}`}
							onClick={() => toggleAccordion(index)}
							type="button"
						>
							<span className='h4'>{faq.question}</span>
						</button>
					</div>
					<div
						className={`accordion-collapse collapse ${activeIndex === index ? 'show' : ''}`}
					>
						<div className="accordion-body lh-base small" dangerouslySetInnerHTML={{ __html: faq.answer }} />
					</div>
				</div>
			))}
		</div>
	);

	return (
		<>
			<div className='red text-white'>
				<div className="d-flex justify-content-center align-items-center overflow-hidden red mx-5 pb-5">
					<div className="cards mt-5 pt-5 pb-5">
						<div className='row'>
							<div className="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12">
								<div className="section_4_part_2 d-lg-flex flex-row mb-5">

									<div className='py-5 p-lg-5'>
										<div className="body-text display-4 fw-bold">Moolah Capital Research</div>
										<p className="body-text">
											As a fund management team creating a crypto index, Moolah methodology for the Income fund focuses on selecting stable, liquid coins with sufficient historical data for backtesting. We prioritize assets with high market capitalization and consistent trading volumes to ensure liquidity and smooth execution.
										</p>
										<p className='body-text pb-5'>
											Stablecoins are considered for their volatility-reducing properties, while established cryptocurrencies like Bitcoin and Ethereum provide a solid foundation. We also evaluate technological soundness, regulatory compliance, and community support to mitigate risks. Reliable data sources are crucial for accurate backtesting and performance analysis. Finally, we implement a regular review and rebalancing process to adapt to market shifts and maintain the index's integrity.
										</p>
									</div>

									<img
										alt="Research Methodology"
										className="w-mobile-full w-40"
										src={img1}
										onError={(e) => {
											e.target.onerror = null; // Prevent infinite loop
											e.target.src = "/placeholder.jpg";
										}}
									/>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>


			<div className='text-black'>

				<div className='py-5 my-5'>
					<div className='container py-5 mt-5'>
						<h1 className='display-4 fw-bold pb-5 body-text'>Moolah Capital Research</h1>

						<div className='row body-text'>
							<div className='col-md-4 p-4'>
								<div className='fw-bold mb-2'>
									Passive funds
								</div>
								<p>
									They invest by market indices by buying and selling assets that are included in the target index.
								</p>
							</div>
							<div className='col-md-4 p-4'>
								<div className='fw-bold mb-2'>
									Smart Beta funds
								</div>
								<p>
									They are active funds that have the goal to beat the market (“beta”) by ad-hoc investment strategies.
								</p>
							</div>
							<div className='col-md-4 p-4'>
								<div className='fw-bold mb-2'>
									Special situations funds
								</div>
								<p>
									They active funds are built by professional fund managers around special themes and market segments.
								</p>
							</div>
						</div>
					</div>
				</div>

				<div className="py-5 red-2 text-white mb-5">
					<div className='container py-5 mb-5'>
						<div className='py-5'>
							<h1 className='body-text display-4 fw-bold my-5'>Risk Management</h1>
							{renderRiskManagement(riskManagementContent, activeIndex2, toggleAccordion2)}
						</div>
						<div className='py-5'>
							{renderCardSections(cardContent, activeIndex3, toggleAccordion3)}
						</div>
					</div>
				</div>

				<div className="section1 my-5 py-5">
					<div className="container mb-5">
						<h1 className='body-text display-4 fw-bold black my-5'>Frequently Asked Questions</h1>
						{renderAccordion(faqs, activeIndex1, toggleAccordion1)}
					</div>
				</div>


			</div>
		</>

	);
}

export default Research;
