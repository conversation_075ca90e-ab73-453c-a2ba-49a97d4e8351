import React, { useState } from 'react';
import useAssetCache from '../../context/useAssetCache.js';
import CTASection from '../../App/CTA/CTASection.jsx';
// Import global colors and Stripe effects
import { COLORS } from '../../constants/colors';
import { applyPreset } from '../Common/StripeBackgroundConfig';

// Use global colors directly
const FAQ_COLORS = COLORS;

const Faq = () => {
	// States for each section
	const imgFaq = useAssetCache('imgFaq', '/faq-image.webp');

	// States for accordion
	const [activeIndex1, setActiveIndex1] = useState(null);
	const [activeIndex2, setActiveIndex2] = useState(null);
	const [activeIndex3, setActiveIndex3] = useState(null);
	const [activeIndex4, setActiveIndex4] = useState(null);
	const [activeIndex5, setActiveIndex5] = useState(null);

	// Toggle functions for each section
	const toggleAccordion1 = index => {
		setActiveIndex1(activeIndex1 === index ? null : index);
	};

	const toggleAccordion2 = index => {
		setActiveIndex2(activeIndex2 === index ? null : index);
	};

	const toggleAccordion3 = index => {
		setActiveIndex3(activeIndex3 === index ? null : index);
	};

	const toggleAccordion4 = index => {
		setActiveIndex4(activeIndex4 === index ? null : index);
	};
	const toggleAccordion5 = index => {
		setActiveIndex5(activeIndex5 === index ? null : index);
	};

	const faq = [
		{
			question: 'What is a fund?',
			answer:
				'A fund is an investment vehicle that pools together the money from many individuals. Fund managers then use it to invest in a wide range of crypto assets. Each investor is issued units, which represent a portion of the holdings of the fund.',
		},
		{
			question: 'What is the difference between an accumulation and income fund?',
			answer:
				"The type of unit you hold determines how any income generated from the fund's underlying investments is treated. With income units, income is paid out to fund holders as cash, which could provide the investor with an income stream. With accumulation units, income is retained within the fund and reinvested, increasing the price of the units.",
		},
		{
			question: 'How can I buy funds?',
			answer:
				"You can buy funds online. Please ensure you have read the fund's Key Investor Information Document (KIID) or Key Features first. If you're registered for online access, log in, select the relevant account, and click the ‘Invest Now’ button.",
		},
		{
			question: 'Why invest in funds?',
			answer:
				'Funds are popular with investors because they offer access to a ready-made investment portfolio run by an expert in their field. You get instant access to a diversified portfolio for a much lower cost than purchasing the individual investments yourself.',
		},
		{
			question: 'How do I sell funds?',
			answer:
				'You can sell your funds online by logging into your account, selecting the account with the funds you want to sell, and following the on-screen instructions to complete the transaction.',
		},
	];

	const faq2 = [
		{
			question: 'What is the difference between fund and portfolio?',
			answer:
				'Think of a portfolio as your personal vault full of all your investments. Some of these investments could be funds, which are like multipacks of various cryptocurrencies, DeFi instruments and other type of investments.',
		},
		{
			question: 'What is DeFi?',
			answer:
				'DeFi is a class of financial products that are offered through blockchains. </br> "DeFi" stands for "decentralized finance" and got its name because blockchains are decentralized databases that record transactions and aren&apos;t controlled or managed by any central authority. </br> By design, DeFi products opera without brokers and intermediaries, which are instead what makes traditional finance possible. Without brokers and intermediaries, charging fees at every turn; financial transactions become faster, more affordable, more transparent, and best of all more accessible. </br> All these advantages made DeFi companies and DeFi financial instruments very attractive for users and also for investors.',
		},
		{
			question:
				'Why investing in cryptocurrencies and DeFi is more complicated than traditional assets like stocks and bonds?',
			answer:
				'By design, blockchains instruments do not use financial intermediaries who in traditional finance demand a fee for helping you buying or selling products, and keeping them safe for you while you invest. </br> For that, the entire investment process  is left to you. This makes investing cheaper and more transparent but also less immediate.  </br> On top of that, blockchain instruments and crypto assets are created by crypto protocols; they cannot be simply bought and sold like normal investments and require technical knowledge that is often beyond non-professional crypto investors. </br> Invest with Moolah funds to simplify your investing in crypto.',
		},
		{
			question:
				'What is the difference between financial assets and instruments?',
			answer:
				'Oversimplifying, they can be used as synonyms. More correctly, financial instruments generate financial assets. For that, their meaning overlaps. </br> <ul><li>Financial assets refer to assets that arise from contractual agreements on future cash flows or from owing equity instruments of another entity.</li></ul> <ul><li>Financial instruments refer to a contract that generates a financial asset to one of the parties involved, and an equity instrument or financial liability to the other entity.</li></ul>',
		},
		{
			question: 'What is diversification and why is important?',
			answer:
				"Ever heard the saying, Don't put all your eggs in one basket? That's what diversification is all about It's a smart way to spread your investments across the market and around the world, so you're not relying on just one region, industry or asset type. This can help boost your potential returns without adding extra risk.",
		},
	];

	const faq3 = [
		{
			question: 'What is a Moolah fund?',
			answer:
				'Moolah funds are a typical sub-category of funds. </br> As traditional funds, Moolah funds pool together the money from many individuals. </br> While traditional funds are 100% invested by fund managers who look after the fund in exchange for a compensation, Moolah funds invest by indexes and automated strategies. </br> This makes Moolah funds cheaper and more transparent.',
		},
		{
			question: 'What is index investing?',
			answer:
				'An index measures the combined performance of a collection of assets. For example, the 100 largest companies by market capitalisation.  </br> An index fund will typically buy assets, and try to maintain this, in every company listed on the index it’s tracking.  </br> In simpler words, index investing consists of buys shares of a fund that replicates a targeted market index.',
		},
		{
			question: 'What indexes are Moolah funds tracking?',
			answer:
				'Moolah funds track the Moolah indexes. </br> Moolah indexes summarize crypto market segments, themes and other sub-categories.',
		},
		{
			question: 'Why investing in Moolah funds?',
			answer:
				"“Don't look for the needle in the haystack. Just buy the haystack!” (John C. Bogle, The Little Book of Common Sense Investing: The Only Way to Guarantee Your Fair Share of Stock Market Returns). </br> You can’t invest directly in an index, but you can invest in an index fund, so in a fund that replicates that index.",
		},
		{
			question: 'Can you trade Moolah funds like normal ETFs?',
			answer:
				'At this stage,  Moolah funds will not be listed on an exchange, they can be traded throughout the day like other crypto assets. </br> ETFs can encourage short-term trading, with investors moving from one strategy to another rather than fostering a buy-and-hold mentality. </br> Frequent trading can be detrimental to an investor’s returns over a long-term horizon. </br> Moolah funds could be traded like normal ETFs in the future.',
		},
	];

	const faq4 = [
		{
			question: 'I am not sure about what to buy. Can Moolah help me?',
			answer:
				'Yes! Reach out via email and we will help you with answering your questions and clarifying your doubts.',
		},
		{
			question: 'What fund should I buy based on my goal?',
			answer:
				'Everyone has different financial goals and needs. <ul><li>If you want to grow your wealth for a medium/long term future - like a pension, a sum to use for an important purchase - an accumulation type fund would work better for you.</li><li>If you need a monthly income, then an income fund is the best type of fund you should invest in.</li></ul> Please note that you can switch from an accumulation fund to an income fund and vice versa.',
		},
		{
			question: 'Can I add more than one fund to my portfolio?',
			answer:
				'Yes you can invest in how many funds you wish. Adding more funds will increase the diversification of your portfolio. You could also mix and match investment goals by adding an accumulation fund and an income fund to your portfolio to gain an income and future wealth.',
		},
		{
			question: 'Can I use Moolah Capital for daily trading?',
			answer:
				'You could but we believe our funds work best when your money is working over a medium to long-term investment horizon. </br> Reach out and see how you could incorporate Moolah Research trading signals and reports in your daily investing if you are a trader.',
		},
		{
			question: 'Can I close funds back before I expected?',
			answer:
				'No fear, we do not own your money and there is no lock-in period. You can withdraw your capital in any moment, with no explanations needed or forms to fill. You can get your money back transferred to your preferred account by simply selling the fund. </br> You can keep your account open if you wish to come back and invest with us again in the future. </br> Along with the market value of your investment, we will also transfer all the earnings that your capital will have matured at that point in time when you sell the fund.',
		},
	];
	const faq5 = [
		{
			question: 'How much will I have to pay as fees for my funds?',
			answer:
				'We offer our funds at a very competitive fees: 0.25% for fund custody and 2% for fund management. </br> No hidden fees otherwise.',
		},
		{
			question: 'How do I pay transaction costs?',
			answer:
				"Funds aim to ensure that trading costs are spread fairly between the different investors in a fund - existing investors, new investors putting money in, and departing investors taking money out. </br> In particular, they aim to protect existing investors from the costs of any trading which is caused by new investors and departing investors. </br> They have two different ways of doing this. Some funds retain the right to apply a “dilution levy” and others a and others a “bid/offer spread” <ul><li>A dilution levy is an explicit allocation of the fund's trading costs to the new investments. For example, if a new investment of £10,000 created £20 of trading costs for the fund, this £20 could be passed on explicitly to the new investors as a dilution levy. </li> <li>A bid/offer spread means that new investments pay a higher price for units, which indirectly contributes to the fund's trading costs. For example, if a new investment of £10,000 created £20 of trading costs for the fund, the unit price might be 0.2% higher.</li></ul> These work in different ways, but they both result in the value of units purchased being less than the amount invested. Which is why it is usually not sensible to switch investments too frequently.",
		},
		{
			question: 'What is the dilution levy?',
			answer:
				" A dilution levy is an allocation of a fund's trading costs to the investments which have created those costs. It is used to protect the majority of investors from the costs of trading by a minority. </br> Without a dilution levy, these trading costs would be paid by the fund, which would disadvantage existing investors (as they are effectively paying someone else's trading costs). </br> It is not paid to any third party but goes directly into the fund to be shared across all investors.</br> For example, if a new investment of £10,000 created £20 of trading costs for the fund, this £20 could be passed on explicitly as a dilution levy, meaning the total invested would be £9,980. The £20 dilution levy would then be paid into the fund, compensating it for the trading costs. </br> Not all funds apply dilution levies and funds often make no adjustment on the basis that investors' overall trading costs are generally similar, so sharing them between everyone is fair overall. </br> The amount also varies depending depends on the type of assets the fund invests in. For example, it will usually be higher for funds that invest in assets that are harder (and hence more expensive) to trade, such as small companies or property. </br> When funds do apply a dilution levy it is typically between 0% and 2% of the transaction.",
		},
		{
			question: 'What is the bid/offer spread?',
			answer:
				"A bid/offer spread means that new investments pay a slightly higher price for units. This indirectly contributes to the trading costs incurred by the fund when investing the new money. It is used to protect the majority of investors from the costs of trading by a minority. </br> Without a bid/offer spread, these trading costs would be paid by the fund, which would disadvantage existing investors (as they are effectively paying someone else's trading costs). </br> For example, if a new investment of £10,000 created £20 of trading costs for the fund, the unit price might be set 0.2% higher, meaning the value of units purchased was only £9,980 (with the remaining £20 used by the fund to cover its cost). </br> Not all funds apply bid/offer spreads and some funds often make no adjustment on the basis that investors' overall trading costs are generally similar, so sharing them between everyone is fair overall. </br>The amount also varies depending on the type of assets the fund invests in. For example, it will usually be higher for funds that invest in assets that are harder (and hence more expensive) to trade, such as small companies or property. </br> When funds do apply a bid/offer spread it is typically between 0% and 2% of the unit price, but can occasionally be higher.",
		},
		{
			question: 'What is the difference between funds and accounts costs?',
			answer:
				"Costs matter because every pound you pay in costs eats into your future returns. What's more, just like returns, the impact of costs compounds over time. </br> Investing charges might not sound like big numbers. But if you're getting a return of 4% and paying 2% in charges, that's half of your returns gone. </br> When you invest you normally pay 2 broad types of charges: fund and account-level. </br> Fund costs are designed to cover the cost of running the fund </br> They vary by fund – the ongoing charges figure (OCFs) range is capped to 2%. </br> For a $10,000 investment would come to just $200 a year based on our fund-range average. </br> Account costs are designed to cover the cost of running our service, the associated technology and our communications. </br> Account costs start at £5 a month, capped at $500. </br> For a $10,000 investment would be just $20 each year.",
		},
	];

	const renderAccordion = (faqs, activeIndex, toggleAccordion, sectionBackground) => (
		<div className="accordion" id="accordionFlushExample" style={{ gap: '8px', display: 'flex', flexDirection: 'column' }}>
			{faqs.map((faq, index) => {
				// Use contrasting blue background for accordion
				const accordionBackground = sectionBackground === FAQ_COLORS.DARK_BLUE ? FAQ_COLORS.MEDIUM_BLUE : FAQ_COLORS.DARK_BLUE;
				// Set text color based on accordion background
				const textColor = accordionBackground === FAQ_COLORS.MEDIUM_BLUE ? 'var(--text-medium-blue)' : 'var(--text-white)';
				// Set shadow color based on accordion background
				const shadowColor = accordionBackground === FAQ_COLORS.MEDIUM_BLUE ? 'var(--black-10)' : 'var(--white-10)';

				return (
					<div className="accordion-item" key={index} style={{
						backgroundColor: accordionBackground,
						border: 'none',
						borderRadius: '12px',
						boxShadow: `0 2px 8px ${shadowColor}`,
						marginBottom: '0px',
						overflow: 'hidden',
						transition: 'transform 0.2s ease, box-shadow 0.2s ease'
					}}>
						<h2 className="accordion-header" id={`flush-heading${index}`}>
							<button
								className={`accordion-button ${activeIndex === index ? '' : 'collapsed'}`}
								type="button"
								onClick={() => toggleAccordion(index)}
								aria-expanded={activeIndex === index}
								aria-controls={`flush-collapse${index}`}
								style={{
									backgroundColor: accordionBackground,
									color: textColor,
									border: 'none',
									boxShadow: 'none',
									borderRadius: activeIndex === index ? '12px 12px 0 0' : '12px',
									padding: '16px 20px',
									fontWeight: '500',
									transition: 'all 0.2s ease'
								}}
								onMouseEnter={(e) => {
									e.target.closest('.accordion-item').style.transform = 'translateY(-2px)';
									e.target.closest('.accordion-item').style.boxShadow = `0 4px 12px ${shadowColor}`;
								}}
								onMouseLeave={(e) => {
									e.target.closest('.accordion-item').style.transform = 'translateY(0)';
									e.target.closest('.accordion-item').style.boxShadow = `0 2px 8px ${shadowColor}`;
								}}
							>
								{faq.question}
							</button>
						</h2>
						<div
							id={`flush-collapse${index}`}
							className={`accordion-collapse collapse ${activeIndex === index ? 'show' : ''}`}
							aria-labelledby={`flush-heading${index}`}
						>
							<div className="accordion-body" style={{
								backgroundColor: accordionBackground,
								color: textColor,
								padding: '16px 20px 20px 20px',
								borderRadius: '0 0 12px 12px'
							}}>
								<div dangerouslySetInnerHTML={{ __html: faq.answer }} />
							</div>
						</div>
					</div>
				);
			})}
		</div>
	);

	return (
		<>
			{/* CSS to eliminate white margins and remove hero-content gap */}
			<style>
				{`
					.faq_main {
						margin-bottom: 0 !important;
						padding-bottom: 0 !important;
					}

					#faq-cta {
						margin-bottom: 0 !important;
						padding-bottom: 0 !important;
					}

					.full-width-dark-section, .full-width-medium-section {
						margin-bottom: 0 !important;
						padding-bottom: 0 !important;
					}

					.page-content-container {
						margin-top: 0 !important;
						padding-top: 0 !important;
						margin-bottom: 0 !important;
						padding-bottom: 0 !important;
					}

					/* Target any section that might be the last element */
					section:last-of-type {
						margin-bottom: 0 !important;
						padding-bottom: 0 !important;
					}

					/* Target the outer div containers */
					body > div > div:last-child {
						margin-bottom: 0 !important;
						padding-bottom: 0 !important;
					}

					/* Force all elements to have no bottom margin */
					* {
						margin-bottom: 0 !important;
					}

					/* Restore necessary margins for content readability */
					p, h1, h2, h3, h4, h5, h6, li {
						margin-bottom: 1rem !important;
					}

					/* But ensure the page itself has no bottom margin */
					html, body {
						margin-bottom: 0 !important;
						padding-bottom: 0 !important;
					}

					/* Negative margin fallback to pull footer up */
					footer {
						margin-top: -20px !important;
					}
				`}
			</style>

			{/* Hero Section - Reduced height like other pages */}
			<section style={{
				backgroundImage: 'linear-gradient(var(--black-50), var(--black-50)), url("/BTC%20FAQ.jpg")',
				backgroundSize: 'cover',
				backgroundPosition: 'center',
				backgroundRepeat: 'no-repeat',
				minHeight: '70vh',
				display: 'flex',
				alignItems: 'center',
				justifyContent: 'center',
				position: 'relative'
			}}>
				<div className="container">
					<div className="body-text hero-main-content">
						<h1 className="display-4 text-white text-center mb-4">Frequently Asked Questions</h1>
					</div>
				</div>
			</section>

			<div className="page-content-container" id="faq-content">
				{/* Section 1 - Investing in Funds */}
				<section className="py-5 full-width-dark-section" style={{ backgroundColor: FAQ_COLORS.DARK_BLUE, minHeight: '500px', paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0', display: 'flex', alignItems: 'center', position: 'relative' }}>
					{/* Floating bubbles for Investing in Funds section */}
					<div className="fx-bubbles" aria-hidden="true" style={{
						position: 'absolute',
						top: 0,
						left: 0,
						right: 0,
						bottom: 0,
						zIndex: 0,
						pointerEvents: 'none'
					}}>
						<span className="b"></span>
						<span className="b"></span>
						<span className="b"></span>
						<span className="b"></span>
						<span className="b"></span>
					</div>

				<div className="container text-white" style={{ paddingLeft: '20px', paddingRight: '20px', position: 'relative', zIndex: 1 }}>
					<div className='row position-relative py-2'>
						<div className='col-lg-4'>
							<div className="text-start mb-5 d-flex align-items-center" style={{ gap: '0.5rem' }}>
								<div className="line d-none d-md-block" style={{ backgroundColor: 'white' }}></div>
								<div className='h2c display-6 fw-bold text-white mb-0'>
									Investing in Funds
								</div>
							</div>
						</div>
						<div className="section1 col-lg-8">
							{renderAccordion(faq, activeIndex1, toggleAccordion1, FAQ_COLORS.DARK_BLUE)}
						</div>
					</div>
				</div>
			</section>

			{/* Section 2 - Understanding Investments */}
			<section className="py-5 full-width-medium-section" style={{ backgroundColor: FAQ_COLORS.MEDIUM_BLUE, minHeight: '500px', paddingTop: '60px', paddingBottom: '60px', display: 'flex', alignItems: 'center', color: 'var(--text-medium-blue)', position: 'relative' }}>
				{/* Floating bubbles for Understanding Investments section */}
				<div className="fx-bubbles" aria-hidden="true" style={{
					position: 'absolute',
					top: 0,
					left: 0,
					right: 0,
					bottom: 0,
					zIndex: 0,
					pointerEvents: 'none'
				}}>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
				</div>

				<div className="container" style={{ color: 'var(--text-medium-blue)', paddingLeft: '20px', paddingRight: '20px', position: 'relative', zIndex: 1 }}>
					<div className='row position-relative py-2'>
						<div className='col-lg-4'>
							<div className="text-start mb-5 d-flex align-items-center" style={{ gap: '0.5rem' }}>
								<div className="line d-none d-md-block" style={{ backgroundColor: 'var(--text-medium-blue)' }}></div>
								<div className='h2c display-6 fw-bold mb-0' style={{ color: 'var(--text-medium-blue)' }}>
									Understanding Investments
								</div>
							</div>
						</div>
						<div className="section1 col-lg-8">
							{renderAccordion(faq2, activeIndex2, toggleAccordion2, FAQ_COLORS.MEDIUM_BLUE)}
						</div>
					</div>
				</div>
			</section>

			{/* Section 3 - Understanding Moolah Funds */}
			<section className="py-5 full-width-dark-section" style={{ backgroundColor: FAQ_COLORS.DARK_BLUE, minHeight: '500px', paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0', display: 'flex', alignItems: 'center', position: 'relative' }}>
				{/* Floating bubbles for Understanding Moolah Funds section */}
				<div className="fx-bubbles" aria-hidden="true" style={{
					position: 'absolute',
					top: 0,
					left: 0,
					right: 0,
					bottom: 0,
					zIndex: 0,
					pointerEvents: 'none'
				}}>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
				</div>

				<div className="container text-white" style={{ paddingLeft: '20px', paddingRight: '20px', position: 'relative', zIndex: 1 }}>
					<div className='row position-relative py-2'>
						<div className='col-lg-4'>
							<div className="text-start mb-5 d-flex align-items-center" style={{ gap: '0.5rem' }}>
								<div className="line d-none d-md-block" style={{ backgroundColor: 'white' }}></div>
								<div className='h2c display-6 fw-bold text-white mb-0'>
									Understanding Moolah Funds
								</div>
							</div>
						</div>
						<div className="section1 col-lg-8">
							{renderAccordion(faq3, activeIndex3, toggleAccordion3, FAQ_COLORS.DARK_BLUE)}
						</div>
					</div>
				</div>
			</section>

			{/* Section 4 - Investment Goals */}
			<section className="py-5 full-width-medium-section" style={{ backgroundColor: FAQ_COLORS.MEDIUM_BLUE, minHeight: '500px', paddingTop: '60px', paddingBottom: '60px', display: 'flex', alignItems: 'center', color: 'var(--text-medium-blue)', position: 'relative' }}>
				{/* Floating bubbles for Investment Goals section */}
				<div className="fx-bubbles" aria-hidden="true" style={{
					position: 'absolute',
					top: 0,
					left: 0,
					right: 0,
					bottom: 0,
					zIndex: 0,
					pointerEvents: 'none'
				}}>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
				</div>

				<div className="container" style={{ color: 'var(--text-medium-blue)', paddingLeft: '20px', paddingRight: '20px', position: 'relative', zIndex: 1 }}>
					<div className='row position-relative py-2'>
						<div className='col-lg-4'>
							<div className="text-start mb-5 d-flex align-items-center" style={{ gap: '0.5rem' }}>
								<div className="line d-none d-md-block" style={{ backgroundColor: 'var(--text-medium-blue)' }}></div>
								<div className='h2c display-6 fw-bold mb-0' style={{ color: 'var(--text-medium-blue)' }}>
									Investment Goals
								</div>
							</div>
						</div>
						<div className="section1 col-lg-8">
							{renderAccordion(faq4, activeIndex4, toggleAccordion4, FAQ_COLORS.MEDIUM_BLUE)}
						</div>
					</div>
				</div>
			</section>

			{/* Section 5 - Investment Costs */}
			<section className="py-5 full-width-dark-section" style={{ backgroundColor: FAQ_COLORS.DARK_BLUE, minHeight: '500px', paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0', display: 'flex', alignItems: 'center', position: 'relative' }}>
				{/* Floating bubbles for Investment Costs section */}
				<div className="fx-bubbles" aria-hidden="true" style={{
					position: 'absolute',
					top: 0,
					left: 0,
					right: 0,
					bottom: 0,
					zIndex: 0,
					pointerEvents: 'none'
				}}>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
				</div>

				<div className="container text-white" style={{ paddingLeft: '20px', paddingRight: '20px', position: 'relative', zIndex: 1 }}>
					<div className='row position-relative py-2'>
						<div className='col-lg-4'>
							<div className="text-start mb-5 d-flex align-items-center" style={{ gap: '0.5rem' }}>
								<div className="line d-none d-md-block" style={{ backgroundColor: 'white' }}></div>
								<div className='h2c display-6 fw-bold text-white mb-0'>
									Investment Costs
								</div>
							</div>
						</div>
						<div className="section1 col-lg-8">
							{renderAccordion(faq5, activeIndex5, toggleAccordion5, FAQ_COLORS.DARK_BLUE)}
						</div>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section id="faq-cta" className="full-width-medium-section" style={{ backgroundColor: FAQ_COLORS.MEDIUM_BLUE, paddingTop: '30px', paddingBottom: '30px', color: 'var(--text-medium-blue)', position: 'relative' }}>
				{/* Floating bubbles for FAQ CTA section */}
				<div className="fx-bubbles" aria-hidden="true" style={{
					position: 'absolute',
					top: 0,
					left: 0,
					right: 0,
					height: '100%',
					zIndex: 0,
					pointerEvents: 'none'
				}}>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
				</div>

				<div className="container text-center-mbl" style={{ color: 'var(--text-medium-blue)', paddingLeft: '20px', paddingRight: '20px', position: 'relative', zIndex: 1 }}>
					<CTASection
						cta="View Funds"
						link="/funds"
						title="View more on Moolah Funds"
						theme='learn'
						titleStyle={{ color: 'var(--text-medium-blue)' }}
					/>
				</div>
			</section>
		</div>
		</>
	);
};

export default Faq;
