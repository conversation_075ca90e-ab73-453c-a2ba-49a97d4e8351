import React from 'react';
import { FaFileAlt } from 'react-icons/fa';
import './fund.css';
import { COLORS, SEMANTIC_COLORS } from '../../constants/colors';

const Documents = ({ fundName }) => {
  const documents = [
    { name: "Fact sheet" },
    { name: "Product disclosure statement" },
    { name: "Annual report" },
    { name: "Half-year financial report" },
    { name: "PDS reference guide" },
    { name: "Performance summary" }
  ];

  return (
    <div style={{
      width: '100vw',
      marginLeft: 'calc(-50vw + 50%)',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* Documents Section */}
      <section style={{
        backgroundColor: COLORS.WHITE,
        paddingTop: '60px',
        paddingBottom: '60px',
        position: 'relative',
        width: '100%'
      }}>
        {/* Floating bubbles */}
        <div className="fx-bubbles" aria-hidden="true" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 0,
          pointerEvents: 'none'
        }}>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
        </div>

        <div className="container" style={{ position: 'relative', zIndex: 1, paddingLeft: '20px', paddingRight: '20px' }}>
          <div className="row justify-content-center mb-5">
            <div className="col-lg-10 text-center">
              <h2 className="h2 mb-4" style={{ color: COLORS.TEXT_BLACK }}>
                {fundName} Documents
              </h2>
            </div>
          </div>

          <div className="row g-4 justify-content-center">
            {documents.map((doc, i) => (
              <div key={i} className="col-md-6 col-lg-4">
                <div className="card h-100" style={{
                  backgroundColor: 'var(--white-10)',
                  border: 'none',
                  borderRadius: 'var(--border-radius-md)',
                  backdropFilter: 'blur(10px)'
                }}>
                  <div className="card-body text-center">
                    <div style={{ marginBottom: '20px' }}>
                      <FaFileAlt style={{
                        fontSize: '2.5rem',
                        color: COLORS.TEXT_BLACK,
                        marginBottom: '15px'
                      }} aria-label="Document icon" />
                      <h5 style={{
                        color: COLORS.TEXT_BLACK,
                        fontWeight: 'bold',
                        margin: 0,
                        fontSize: '1.1rem'
                      }}>{doc.name}</h5>
                    </div>
                    <button style={{
                      backgroundColor: `${COLORS.RED} !important`,
                      color: `${COLORS.TEXT_WHITE} !important`,
                      border: 'none !important',
                      borderRadius: '8px',
                      padding: '12px 20px',
                      fontSize: '1rem',
                      fontWeight: '500',
                      cursor: 'pointer',
                      width: '100%'
                    }}>
                      Download <span style={{ fontSize: '1rem' }}>⬇️</span>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};
export default Documents;