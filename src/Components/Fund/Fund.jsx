import React from 'react';
import './fund.css';
import Overview from './Overview.js';
import { COLORS } from '../../constants/colors';

/*
const formatCurrency = (value) =>
  typeof value === 'number' ? `$${(value / 1_000_000).toFixed(1)}M` : 'N/A';
*/
const Fund = ({
  fundName,
  holdings = [],
  fundDetails = {},
  performanceData = null,
  annualPerformanceData = [],
  lineChartOptions = {}
}) => {
  return (
    <div className="fund-container fund-main-container" style={{
      marginTop: window.innerWidth <= 768 ? '0px' : '0px', // No extra spacing from navbar
      backgroundColor: COLORS.MEDIUM_BLUE,
      minHeight: '100vh',
      margin: 0,
      padding: 0
    }}>
      {/* Tabs are now integrated into Overview component */}
      <Overview
        fundDetails={fundDetails}
        fundName={fundName}
        holdings={holdings}
        performanceData={performanceData}
        annualPerformanceData={annualPerformanceData}
        lineChartOptions={lineChartOptions}
      />
    </div>
  );
};

export default Fund;
