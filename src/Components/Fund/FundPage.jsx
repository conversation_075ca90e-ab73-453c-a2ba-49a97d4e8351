import React, { useEffect, useState } from 'react';
import Fund from './Fund';
import { useContent } from '../../context/ContentContext';
import { processPerformanceData } from '../../utils/performanceDataProcessor';
import monthlyFundsData from '../../data/moolah-funds-monthly-2025-05.json';
import './fund-page.css';

const FundPage = ({ fund }) => {
  const [fundData, setFundData] = useState(null);
  const { content } = useContent();

  useEffect(() => {

    // Find the fund data from the content context
    if (content && content.funds) {
      const foundFund = content.funds.find(f => f.id === fund);
      if (foundFund) {
        setFundData(foundFund);
      } else {
        console.warn(`Fund data not found for: ${fund}`);
      }
    }

    return () => {
      console.log('FundPage component unmounting');
    };
  }, [fund, content]);

  useEffect(() => {
    // Function to remove unwanted text nodes
    const removeUnwantedTextNodes = () => {
      const pageContainer = document.querySelector('.page-container');
      if (pageContainer) {
        // Get all child nodes
        const childNodes = Array.from(pageContainer.childNodes);
        
        // Remove any text nodes that are direct children of page-container
        childNodes.forEach(node => {
          if (node.nodeType === Node.TEXT_NODE || 
              (node.nodeType === Node.ELEMENT_NODE && 
               !node.classList.contains('fund-main-container'))) {
            pageContainer.removeChild(node);
          }
        });
      }
    };
    
    // Run on mount and whenever fundData changes
    removeUnwantedTextNodes();
    
    // Also run after a short delay to catch any dynamically added content
    const timeoutId = setTimeout(removeUnwantedTextNodes, 100);
    
    return () => clearTimeout(timeoutId);
  }, [fundData]);

  // If fund data is not loaded yet, show loading
  if (!fundData) {
    return <div>Loading fund data...</div>;
  }

  // Generate fund details based on the loaded fund data
  const fundDetails = {
    name: `${fundData.name} Fund`,
    aum: 'NA',
    start: fundData.inception || '2020-01-01',
    Fee: fundData.fee || '2.0%',
    strategy: fundData.shortDescription,
    manager: fundData.manager || 'Moolah Capital',
    description: fundData.longDescription || 'The Moolah Capital Crypto Fund is a diversified portfolio of leading cryptocurrencies designed to provide investors with exposure to the growing digital asset class while managing volatility through strategic allocation.',
    investmentObjective: fundData.investmentObjective || 'To achieve long-term capital appreciation by investing in a diversified portfolio of cryptocurrencies and blockchain-related assets.',
    keyFeatures: fundData.features || [
      'Diversified exposure to top cryptocurrencies',
      'Professional portfolio management',
      'Regular rebalancing to maintain optimal allocation',
      'Secure custody solutions',
      'Transparent fee structure'
    ],
    riskLevel: fundData.riskLevel || 'High',
    performance: {
      oneMonth: fundData.performance?.oneMonth || 0.08,
      threeMonth: fundData.performance?.threeMonth || 0.08,
      sixMonth: fundData.performance?.sixMonth || 0.08,
      oneYear: fundData.performance?.oneYear || 0.08,
      threeYear: fundData.performance?.threeYear || 0.08,
      fiveYear: fundData.performance?.fiveYear || 0.08
    }
  };

  // Sample holdings data - ideally this would come from an API or the content context
  const holdings = [
    { name: 'Bitcoin', price: 88.88, symbol: 'BTC', allocation: 35, value: 40000, category: 'Blockchain', coin: 'BTC' },
    { name: 'Ethereum', price: 88.88, symbol: 'ETH', allocation: 15, value: 30000, category: 'Blockchain', coin: 'ETH' },
    { name: 'Solana', price: 88.88, symbol: 'SOL', allocation: 5, value: 15000, category: 'Meme', coin: 'SOL' },
    { name: 'Cardano', price: 88.88, symbol: 'ADA', allocation: 7.5, value: 10000, category: 'Blockchain', coin: 'ADA' },
    { name: 'DogeCoin', price: 88.88, symbol: 'DOGE', allocation: 7.5, value: 5000, category: 'Meme', coin: 'DOGE' },
    { name: 'USDT', price: 0.88, symbol: 'USDT', allocation: 15, value: 5000, category: 'StableCoin', coin: 'USDT' },
    { name: 'USDC', price: 0.88, symbol: 'USDC', allocation: 15, value: 5000, category: 'StableCoin', coin: 'USDC' }
  ];

  // Process performance data from the monthly funds data file
  const { performanceData, annualPerformanceData } = processPerformanceData(monthlyFundsData, fund);

  // Fallback performance data if no data is available from the JSON file
  const fallbackPerformanceData = {
    labels: [
      // 2022
      'Jan 2022', 'Feb 2022', 'Mar 2022', 'Apr 2022', 'May 2022', 'Jun 2022',
      'Jul 2022', 'Aug 2022', 'Sep 2022', 'Oct 2022', 'Nov 2022', 'Dec 2022',
      // 2023
      'Jan 2023', 'Feb 2023', 'Mar 2023', 'Apr 2023', 'May 2023', 'Jun 2023',
      'Jul 2023', 'Aug 2023', 'Sep 2023', 'Oct 2023', 'Nov 2023', 'Dec 2023',
      // 2024
      'Jan 2024', 'Feb 2024', 'Mar 2024', 'Apr 2024', 'May 2024', 'Jun 2024',
      'Jul 2024', 'Aug 2024', 'Sep 2024', 'Oct 2024', 'Nov 2024', 'Dec 2024'
    ],
    datasets: [
      {
        label: 'Fund Performance (%)',
        data: [
          // 2022 monthly data (percentage changes)
          2.3, 1.8, 1.2, 0.8, 0.5, 0.2, -0.2, -1.0, -0.5, 0.1, 0.3, 0.5,
          // 2023 monthly data (percentage changes)
          0.8, 1.0, 1.8, 2.0, 2.2, 2.2, 2.5, 2.8, 3.0, 3.2, 3.5, 3.8,
          // 2024 monthly data (percentage changes)
          4.0, 4.2, 4.5, 4.8, 5.0, 5.2, 5.5, 5.8, 6.0, 6.2, 6.5, 6.8
        ],
        borderColor: '#4BC0C0',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        borderWidth: 2,
        pointRadius: 3,
        tension: 0.3,
        fill: true
      },
      {
        label: 'Benchmark (%)',
        data: [
          // 2022 monthly data (percentage changes)
          1.7, 1.5, 1.0, 0.5, 0.3, 0.0, -0.5, -0.8, -0.3, -0.1, 0.0, 0.3,
          // 2023 monthly data (percentage changes)
          0.5, 0.8, 1.5, 1.6, 1.8, 1.7, 2.0, 2.2, 2.5, 2.6, 2.8, 3.0,
          // 2024 monthly data (percentage changes)
          3.2, 3.5, 3.8, 4.0, 4.2, 4.5, 4.8, 5.0, 5.2, 5.5, 5.8, 6.0
        ],
        borderColor: '#FF6384',
        backgroundColor: 'rgba(255, 99, 132, 0.1)',
        borderWidth: 2,
        borderDash: [5, 5],
        pointRadius: 2,
        tension: 0.3,
        fill: false
      }
    ]
  };

  const fallbackAnnualPerformanceData = {
    labels: ['2022', '2023', '2024'],
    datasets: [
      {
        label: 'Fund Performance',
        data: [8, 25, 55],
        backgroundColor: 'rgba(75, 192, 192, 0.7)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1
      },
      {
        label: 'Benchmark',
        data: [5, 20, 45],
        backgroundColor: 'rgba(255, 99, 132, 0.7)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1
      }
    ]
  };

  const lineChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: false,
        grid: { display: true },
        ticks: {
          callback: function(value) {
            return value + '%';
          }
        },
        title: {
          display: true,
          text: 'Monthly Return (%)'
        }
      },
      x: {
        grid: { display: false }
      }
    },
    plugins: {
      tooltip: {
        callbacks: {
          label: function(context) {
            return context.dataset.label + ': ' + context.parsed.y.toFixed(2) + '%';
          }
        }
      }
    }
  };

  // Debug: Log the data being passed to the Fund component
  console.log('Data being passed to Fund component:', {
    fundData,
    fundDetails,
    holdings,
    performanceData: performanceData || fallbackPerformanceData,
    annualPerformanceData: annualPerformanceData || fallbackAnnualPerformanceData,
    lineChartOptions,
    usingJsonData: !!performanceData
  });

  return (
    <div className="page-container">
      <Fund
        annualPerformanceData={annualPerformanceData || fallbackAnnualPerformanceData}
        fundDetails={fundDetails}
        fundName={fundDetails.name}
        holdings={holdings}
        lineChartOptions={lineChartOptions}
        performanceData={performanceData || fallbackPerformanceData}
      />
    </div>
  );
}

export default FundPage;
