import React, { useEffect } from 'react';
import './fund.css';
import { Pie } from 'react-chartjs-2';
import { Chart as ChartJS,  ArcElement, Filler, Tooltip, Legend } from 'chart.js';

// Register required chart elements and plugins
ChartJS.register(Arc<PERSON><PERSON>, Toolt<PERSON>, Filler, Legend);

// Import global colors
import { COLORS } from '../../constants/colors';

// Use global colors directly
const HOLDINGS_COLORS = COLORS;

const Holdings = ({ fundName, holdings, coinPieData, categoryPieData, chartOptions }) => {
  useEffect(() => {
    // Check if pie chart data exists
    return () => {
      console.log('Holdings component unmounting');
    };
  }, [fundName, holdings, coinPieData, categoryPieData]);
  return (
    <div className="holdings-container" style={{ margin: '30px 0', width: '100%', maxWidth: 'none' }}>
      <div className="mb-4 d-flex align-items-center" style={{ gap: '0.5rem', justifyContent: 'flex-start' }}>
        <div className="line d-none d-md-block" style={{ backgroundColor: HOLDINGS_COLORS.DARK_BLUE, width: '40px', height: '3px' }}></div>
        <h3 style={{
          marginBottom: 0,
          color: HOLDINGS_COLORS.DARK_BLUE,
          fontWeight: 'bold',
          textAlign: 'left'
        }}>
          <span role="img" aria-label="portfolio" style={{ marginRight: '8px' }}>📊</span>
          Current Holdings of the {fundName}
        </h3>
      </div>

      <div style={{
        backgroundColor: HOLDINGS_COLORS.WHITE,
        borderRadius: '12px',
        padding: '20px',
        margin: '20px 0',
        boxShadow: '0 4px 12px rgba(13, 27, 46, 0.1)',
        overflow: 'auto'
      }}>
        <table style={{
          width: '100%',
          borderCollapse: 'collapse',
          backgroundColor: 'transparent'
        }}>
          <thead>
            <tr style={{ backgroundColor: HOLDINGS_COLORS.MEDIUM_BLUE }}>
              <th style={{ padding: '15px', color: HOLDINGS_COLORS.WHITE, textAlign: 'left', borderRadius: '8px 0 0 0' }}>Coin</th>
              <th style={{ padding: '15px', color: HOLDINGS_COLORS.WHITE, textAlign: 'left' }}>Category</th>
              <th style={{ padding: '15px', color: HOLDINGS_COLORS.WHITE, textAlign: 'left' }}>Current Price</th>
              <th style={{ padding: '15px', color: HOLDINGS_COLORS.WHITE, textAlign: 'left', borderRadius: '0 8px 0 0' }}>Valuation</th>
            </tr>
          </thead>
          <tbody>
            {holdings.map((item, index) => (
              <tr key={index} style={{
                backgroundColor: index % 2 === 0 ? HOLDINGS_COLORS.MEDIUM_BLUE : HOLDINGS_COLORS.DARK_BLUE,
                borderBottom: '1px solid rgba(255,255,255,0.1)'
              }}>
                <td style={{ padding: '12px', color: HOLDINGS_COLORS.WHITE, fontWeight: '500' }}>{item.coin}</td>
                <td style={{ padding: '12px', color: HOLDINGS_COLORS.WHITE }}>{item.category}</td>
                <td style={{ padding: '12px', color: HOLDINGS_COLORS.WHITE, fontWeight: '500' }}>${item.price.toLocaleString()}</td>
                <td style={{ padding: '12px', color: HOLDINGS_COLORS.WHITE, fontWeight: '500' }}>${item.value}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div style={{
        display: 'flex',
        gap: '20px',
        margin: '30px 0',
        flexWrap: 'wrap'
      }}>
        <div style={{
          flex: '1',
          minWidth: '300px',
          backgroundColor: HOLDINGS_COLORS.MEDIUM_BLUE,
          borderRadius: '12px',
          padding: '25px',
          boxShadow: '0 4px 12px rgba(13, 27, 46, 0.1)'
        }}>
          <h3 style={{
            color: HOLDINGS_COLORS.WHITE,
            marginBottom: '20px',
            textAlign: 'center',
            fontWeight: 'bold'
          }}>Portfolio by Coin</h3>
          <div className='centered-chart'>
            <Pie data={coinPieData} options={chartOptions} />
          </div>
        </div>
        <div style={{
          flex: '1',
          minWidth: '300px',
          backgroundColor: HOLDINGS_COLORS.DARK_BLUE,
          borderRadius: '12px',
          padding: '25px',
          boxShadow: '0 4px 12px rgba(13, 27, 46, 0.1)'
        }}>
          <h3 style={{
            color: HOLDINGS_COLORS.WHITE,
            marginBottom: '20px',
            textAlign: 'center',
            fontWeight: 'bold'
          }}>Portfolio by Category</h3>
          <div className='centered-chart'>
            <Pie data={categoryPieData} options={chartOptions} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Holdings;