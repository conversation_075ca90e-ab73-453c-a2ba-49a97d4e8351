import React, { useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import './fund.css';
import { COLORS, SEMANTIC_COLORS } from '../../constants/colors';
import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  BarElement,
  Filler,
  CategoryScale, // for X-axis
  LinearScale,   // for Y-axis
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  LineElement,
  PointElement,
  BarElement,
  Filler,
  CategoryScale,
  LinearScale,
  Tooltip,
  Legend
);



const Performance = ({ fundName, performanceData, annualPerformanceData, lineChartOptions }) => {
  useEffect(() => {
    return () => {
      console.log('Performance component unmounting');
    };
  }, [performanceData, annualPerformanceData, lineChartOptions]);

  // Enhanced chart options with white axes text
  const enhancedChartOptions = {
    ...lineChartOptions,
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      ...lineChartOptions?.plugins,
      legend: {
        ...lineChartOptions?.plugins?.legend,
        labels: {
          ...lineChartOptions?.plugins?.legend?.labels,
          color: COLORS.WHITE
        }
      }
    },
    scales: {
      x: {
        ...lineChartOptions?.scales?.x,
        ticks: {
          ...lineChartOptions?.scales?.x?.ticks,
          color: COLORS.WHITE
        },
        grid: {
          ...lineChartOptions?.scales?.x?.grid,
          color: 'var(--white-10)'
        }
      },
      y: {
        ...lineChartOptions?.scales?.y,
        ticks: {
          ...lineChartOptions?.scales?.y?.ticks,
          color: COLORS.WHITE
        },
        grid: {
          ...lineChartOptions?.scales?.y?.grid,
          color: 'var(--white-10)'
        }
      }
    }
  };
  return (
    <div style={{
      width: '100vw',
      marginLeft: 'calc(-50vw + 50%)',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* Performance Section */}
      <section style={{
        backgroundColor: COLORS.DARK_BLUE,
        paddingTop: '60px',
        paddingBottom: '60px',
        position: 'relative',
        width: '100%'
      }}>
        {/* Floating bubbles */}
        <div className="fx-bubbles" aria-hidden="true" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 0,
          pointerEvents: 'none'
        }}>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
        </div>

        <div className="container" style={{ position: 'relative', zIndex: 1, paddingLeft: '20px', paddingRight: '20px' }}>
          <div className="row justify-content-center mb-5">
            <div className="col-lg-10 text-center">
              <h2 className="h2 mb-4" style={{ color: COLORS.TEXT_WHITE }}>
                <span role="img" aria-label="chart" style={{ marginRight: '8px' }}>📈</span>
                {fundName} Performance
              </h2>
            </div>
          </div>
          <div className="row g-4">
            <div className="col-lg-6">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body">
                  <h4 className="card-title mb-3 text-center" style={{ color: COLORS.TEXT_WHITE }}>
                    Performance Trend
                  </h4>
                  <div style={{ position: 'relative', height: '300px' }}>
                    <Line data={performanceData} options={enhancedChartOptions} />
                  </div>
                  <p style={{
                    textAlign: 'center',
                    fontSize: 'var(--font-size-sm)',
                    color: 'var(--white-80)',
                    marginTop: '15px',
                    marginBottom: 0
                  }}>Quarterly returns over the past 3 years</p>
                </div>
              </div>
            </div>

            <div className="col-lg-6">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body">
                  <h4 className="card-title mb-3 text-center" style={{ color: COLORS.TEXT_WHITE }}>
                    Annual Performance Comparison
                  </h4>
                  <div style={{ position: 'relative', height: '300px' }}>
                    <Line data={annualPerformanceData} options={enhancedChartOptions} />
                  </div>
                  <p style={{
                    textAlign: 'center',
                    fontSize: 'var(--font-size-sm)',
                    color: 'var(--white-80)',
                    marginTop: '15px',
                    marginBottom: 0
                  }}>Benchmark returns: 10% (2021), 15% (2022), 25% (2023)</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Performance Metrics Section */}
      <section style={{
        backgroundColor: COLORS.DARK_BLUE,
        paddingTop: '40px',
        paddingBottom: '60px',
        position: 'relative',
        width: '100%'
      }}>
        <div className="container" style={{ position: 'relative', zIndex: 1, paddingLeft: '20px', paddingRight: '20px' }}>
          <div className="row justify-content-center mb-4">
            <div className="col-lg-10 text-center">
              <h3 style={{ color: COLORS.TEXT_WHITE, fontWeight: 'bold' }}>
                <span role="img" aria-label="metrics" style={{ marginRight: '8px' }}>📊</span>
                Performance Metrics
              </h3>
            </div>
          </div>

          <div className="row justify-content-center">
            <div className="col-lg-6">
              <div style={{
                backgroundColor: 'var(--white-10)',
                borderRadius: 'var(--border-radius-md)',
                padding: '15px',
                display: 'flex',
                justifyContent: 'center'
              }}>
                <table style={{
                  borderCollapse: 'collapse',
                  backgroundColor: 'transparent',
                  margin: '0',
                  width: 'auto',
                  minWidth: '400px'
                }}>
                  <thead>
                    <tr style={{ backgroundColor: 'var(--white-20)' }}>
                      <th style={{ padding: '12px 8px', color: COLORS.TEXT_WHITE, textAlign: 'center', borderRadius: '8px 0 0 0' }}>Metric</th>
                      <th style={{ padding: '12px 8px', color: COLORS.TEXT_WHITE, textAlign: 'center' }}>Fund</th>
                      <th style={{ padding: '12px 8px', color: COLORS.TEXT_WHITE, textAlign: 'center' }}>Benchmark</th>
                      <th style={{ padding: '12px 8px', color: COLORS.TEXT_WHITE, textAlign: 'center', borderRadius: '0 8px 0 0' }}>Difference</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr style={{ borderBottom: '1px solid var(--white-10)' }}>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: '500', textAlign: 'center' }}>Cumulative Return</td>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: '500', textAlign: 'center' }}>105.4%</td>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: '500', textAlign: 'center' }}>75.0%</td>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: 'bold', textAlign: 'center' }}>+30.4%</td>
                    </tr>
                    <tr style={{ borderBottom: '1px solid var(--white-10)' }}>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: '500', textAlign: 'center' }}>Annualized Return</td>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: '500', textAlign: 'center' }}>27.1%</td>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: '500', textAlign: 'center' }}>20.5%</td>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: 'bold', textAlign: 'center' }}>+6.6%</td>
                    </tr>
                    <tr style={{ borderBottom: '1px solid var(--white-10)' }}>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: '500', textAlign: 'center' }}>Volatility (Std Dev)</td>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: '500', textAlign: 'center' }}>28.5%</td>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: '500', textAlign: 'center' }}>22.0%</td>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: 'bold', textAlign: 'center' }}>+6.5%</td>
                    </tr>
                    <tr style={{ borderBottom: '1px solid var(--white-10)' }}>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: '500', textAlign: 'center' }}>Sharpe Ratio</td>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: '500', textAlign: 'center' }}>0.95</td>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: '500', textAlign: 'center' }}>0.82</td>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: 'bold', textAlign: 'center' }}>+0.13</td>
                    </tr>
                    <tr>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: '500', textAlign: 'center' }}>Maximum Drawdown</td>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: '500', textAlign: 'center' }}>-18.2%</td>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: '500', textAlign: 'center' }}>-12.5%</td>
                      <td style={{ padding: '10px 8px', color: COLORS.TEXT_WHITE, fontWeight: 'bold', textAlign: 'center' }}>-5.7%</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Performance;