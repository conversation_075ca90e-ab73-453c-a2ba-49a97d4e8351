import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import './fund.css';
import { COLORS } from '../../constants/colors';

const WhatIf = ({ fundName, performanceData, lineChartOptions }) => {
  // State for investment amount with default of $10,000
  const [investmentAmount, setInvestmentAmount] = useState(10000);
  // State for formatted display of the investment amount
  const [formattedAmount, setFormattedAmount] = useState('$10,000');
  // State for cumulative returns
  const [cumulativeReturns, setCumulativeReturns] = useState([]);
  // State for chart data
  const [whatIfData, setWhatIfData] = useState(null);

  // Calculate returns when investment amount changes
  useEffect(() => {

    // Check if performance data exists and has the expected structure
    if (!performanceData || !performanceData.datasets || !performanceData.datasets[0] || !performanceData.datasets[0].data) {
      console.error('Performance data is missing or has invalid structure:', performanceData);
      return;
    }

    // Calculate cumulative returns based on monthly performance data
    const newCumulativeReturns = performanceData.datasets[0].data.reduce((acc, monthlyReturn, index) => {
      // Convert the monthly percentage to a decimal (e.g., 5% becomes 0.05)
      const monthlyReturnDecimal = monthlyReturn / 100;

      if (index === 0) {
        // First month: initial investment + first month return
        return [...acc, investmentAmount * (1 + monthlyReturnDecimal)];
      } else {
        // Subsequent months: previous value + current month return
        const previousValue = acc[index - 1];
        return [...acc, previousValue * (1 + monthlyReturnDecimal)];
      }
    }, []);

    setCumulativeReturns(newCumulativeReturns);

    // Create data for the what-if chart
    const newWhatIfData = {
      labels: performanceData.labels,
      datasets: [
        {
          label: `$${investmentAmount.toLocaleString()} Investment Growth`,
          data: newCumulativeReturns,
          borderColor: COLORS.FRAME_COLOR,
          backgroundColor: `${COLORS.FRAME_COLOR}33`,
          borderWidth: 2,
          pointRadius: 3,
          tension: 0.3,
          fill: true
        }
      ]
    };

    setWhatIfData(newWhatIfData);

    // Format the investment amount for display
    setFormattedAmount('$' + investmentAmount.toLocaleString());
  }, [investmentAmount, performanceData]);

  // Handle slider change
  const handleSliderChange = (e) => {
    setInvestmentAmount(Number(e.target.value));
  };

  // Enhanced chart options with white axes text (same as Performance charts)
  const enhancedChartOptions = {
    ...lineChartOptions,
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      ...lineChartOptions?.plugins,
      legend: {
        ...lineChartOptions?.plugins?.legend,
        labels: {
          ...lineChartOptions?.plugins?.legend?.labels,
          color: COLORS.TEXT_BLACK
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return context.dataset.label + ': $' + context.parsed.y.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
          }
        }
      }
    },
    scales: {
      x: {
        ...lineChartOptions?.scales?.x,
        ticks: {
          ...lineChartOptions?.scales?.x?.ticks,
          color: COLORS.TEXT_BLACK
        },
        grid: {
          ...lineChartOptions?.scales?.x?.grid,
          color: 'var(--white-10)'
        }
      },
      y: {
        ...lineChartOptions?.scales?.y,
        ticks: {
          ...lineChartOptions?.scales?.y?.ticks,
          color: COLORS.TEXT_BLACK,
          callback: function(value) {
            return '$' + value.toLocaleString();
          }
        },
        title: {
          display: true,
          text: 'Value ($)',
          color: COLORS.TEXT_BLACK
        },
        grid: {
          ...lineChartOptions?.scales?.y?.grid,
          color: 'var(--white-10)'
        }
      }
    }
  };

  // Calculate final value and total return
  const finalValue = cumulativeReturns.length > 0 ? cumulativeReturns[cumulativeReturns.length - 1] : 0;
  const totalReturn = investmentAmount > 0 ? ((finalValue - investmentAmount) / investmentAmount) * 100 : 0;
  const totalGain = finalValue - investmentAmount;

  return (
    <>
      {/* CSS for slider thumb styling */}
      <style>
        {`
          .investment-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: ${COLORS.FRAME_COLOR};
            cursor: pointer;
            border: 3px solid white;
            box-shadow: 0 2px 4px var(--black-20);
          }

          .investment-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: ${COLORS.FRAME_COLOR};
            cursor: pointer;
            border: 3px solid white;
            box-shadow: 0 2px 4px var(--black-20);
            -moz-appearance: none;
            appearance: none;
          }
        `}
      </style>

      <div style={{
        width: '100vw',
        marginLeft: 'calc(-50vw + 50%)',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* What-If Section */}
      <section style={{
        backgroundColor: COLORS.MEDIUM_BLUE,
        paddingTop: '60px',
        paddingBottom: '60px',
        position: 'relative',
        width: '100%'
      }}>
        {/* Floating bubbles */}
        <div className="fx-bubbles" aria-hidden="true" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 0,
          pointerEvents: 'none'
        }}>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
        </div>

        <div className="container" style={{ position: 'relative', zIndex: 1, paddingLeft: '20px', paddingRight: '20px' }}>
          <div className="row justify-content-center mb-5">
            <div className="col-lg-10 text-center">
              <h2 className="h2 mb-4" style={{ color: COLORS.TEXT_BLACK }}>
                {fundName} Performance Simulation
              </h2>
            </div>
          </div>

          {/* Investment Slider */}
          <div className="row justify-content-center mb-4">
            <div className="col-lg-8">
              <div style={{
                backgroundColor: 'var(--white-10)',
                borderRadius: 'var(--border-radius-md)',
                padding: '25px',
                backdropFilter: 'blur(10px)'
              }}>
                <label style={{
                  display: 'block',
                  textAlign: 'center',
                  color: COLORS.TEXT_BLACK,
                  fontWeight: 'bold',
                  marginBottom: '15px',
                  fontSize: '1.1rem'
                }} htmlFor="investment-slider">Investment Amount: ${investmentAmount.toLocaleString()}</label>
                <input
                  type="range"
                  id="investment-slider"
                  className="investment-slider"
                  min="10000"
                  max="1000000"
                  step="10000"
                  value={investmentAmount}
                  onChange={handleSliderChange}
                  style={{
                    width: '100%',
                    height: '8px',
                    borderRadius: '5px',
                    background: `linear-gradient(to right, ${COLORS.FRAME_COLOR} 0%, ${COLORS.FRAME_COLOR} ${((investmentAmount - 10000) / (1000000 - 10000)) * 100}%, #ddd ${((investmentAmount - 10000) / (1000000 - 10000)) * 100}%, #ddd 100%)`,
                    outline: 'none',
                    cursor: 'pointer',
                    WebkitAppearance: 'none',
                    appearance: 'none'
                  }}
                />
              </div>
            </div>
          </div>

          {/* Metrics Cards */}
          <div className="row g-4 mb-4">
            <div className="col-md-4">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body text-center">
                  <h5 style={{ color: COLORS.TEXT_BLACK, marginBottom: '10px', fontWeight: 'bold' }}>Initial Investment</h5>
                  <p style={{ fontSize: '1.5rem', fontWeight: 'bold', margin: 0, color: COLORS.TEXT_BLACK }}>${investmentAmount.toLocaleString()}</p>
                </div>
              </div>
            </div>
            <div className="col-md-4">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body text-center">
                  <h5 style={{ color: COLORS.TEXT_BLACK, marginBottom: '10px', fontWeight: 'bold' }}>Current Value</h5>
                  <p style={{
                    fontSize: '1.5rem',
                    fontWeight: 'bold',
                    margin: 0,
                    color: COLORS.TEXT_BLACK
                  }}>
                    ${finalValue.toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    })}
                  </p>
                </div>
              </div>
            </div>
            <div className="col-md-4">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body text-center">
                  <h5 style={{ color: COLORS.TEXT_BLACK, marginBottom: '10px', fontWeight: 'bold' }}>Total Return</h5>
                  <p style={{
                    fontSize: '1.5rem',
                    fontWeight: 'bold',
                    margin: 0,
                    color: COLORS.TEXT_BLACK
                  }}>
                    {totalReturn.toFixed(2)}%
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Chart Section */}
          <div className="row justify-content-center">
            <div className="col-lg-10">
              <div className="card" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body">
                  <h4 className="card-title text-center mb-4" style={{ color: COLORS.TEXT_BLACK, fontWeight: 'bold' }}>
                    Investment Growth Over Time
                  </h4>
                  <div style={{ position: 'relative', height: '300px' }}>
                    {whatIfData && <Line data={whatIfData} options={enhancedChartOptions} />}
                  </div>
                  <p style={{
                    textAlign: 'center',
                    fontSize: '0.9rem',
                    color: COLORS.TEXT_BLACK,
                    marginTop: '15px',
                    marginBottom: 0
                  }}>Monthly growth of a ${investmentAmount.toLocaleString()} investment over the past 3 years</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    </>
  );
};

export default WhatIf;
