import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import FundChart from '../Homepage/FundChart.jsx';
import PortfolioChart from '../Homepage/PortfolioChart.jsx';

import FundsComparisonTable from './FundsComparisonTable.jsx';

// Import global colors
import { COLORS } from '../../constants/colors';

// Import fx-bubbles styles
import '../Common/StripeLikeBackground.css';

// Use global colors directly

const Funds = () => {
  const [videoLoaded, setVideoLoaded] = useState(false);
  const videoRef = useRef(null);

  const scrollToTop = useCallback(() => window.scrollTo(0, 0), []);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) {
      console.error("Video ref is null");
      return;
    }

    //console.log("Video element exists:", video);

    const handleCanPlay = () => {
      console.log("Video can play now");
      // Set playback rate to exactly 0.30 (30% of normal rate)
      video.playbackRate = 0.30;
      console.log("Setting video playback rate to 0.30");

      video.play().catch((err) => {
        console.warn('Autoplay blocked:', err);
      });
      setVideoLoaded(true);
    };

    const handleError = (e) => {
      console.error('Video failed to load:', e);
      setVideoLoaded(true); // Still mark as loaded to remove loading state
    };

    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('error', handleError);

    // Try to manually trigger play and set playback rate
    setTimeout(() => {
      if (video && !videoLoaded) {
        console.log("Attempting manual play");
        // Ensure playback rate is set even if canplay event hasn't fired
        video.playbackRate = 0.30;
        console.log("Setting video playback rate to 0.30 (manual)");

        video.play().catch(err => console.warn("Manual play failed:", err));
      }
    }, 1000);

    return () => {
      if (video) {
        video.removeEventListener('canplay', handleCanPlay);
        video.removeEventListener('error', handleError);
      }
    };
  }, [videoLoaded]);

  // Add a separate effect to handle metadata loading
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleMetadataLoaded = () => {
      // Set playback rate when metadata is loaded
      video.playbackRate = 0.30;
      console.log("Metadata loaded - setting video playback rate to 0.30");
    };

    video.addEventListener('loadedmetadata', handleMetadataLoaded);

    return () => {
      video.removeEventListener('loadedmetadata', handleMetadataLoaded);
    };
  }, []);

  // Fund data for charts
  const fundData = [
    {
      name: "STEADY GUARD Fund",
      performance: 85,
      risk: 30,
      fees: 95,
      liquidity: 90
    },
    {
      name: "SMART GROWTH Fund",
      performance: 78,
      risk: 65,
      fees: 70,
      liquidity: 85
    },
    {
      name: "TREND EXPLORER Fund",
      performance: 82,
      risk: 45,
      fees: 80,
      liquidity: 88
    },
    {
      name: "GENERATIVE AI Fund",
      performance: 87,
      risk: 47,
      fees: 75,
      liquidity: 83
    },
    {
      name: "AlphaGlobal GenAI Fund",
      performance: 85,
      risk: 48,
      fees: 76,
      liquidity: 85
    },
    {
      name: "Moolah Capital Portfolio",
      performance: 90,
      risk: 48,
      fees: 74,
      liquidity: 82
    }
  ];



  const panels = useMemo(() => [
    {
      title: 'Passive Index',
      desc: `<p>The passive fund follow a diversified mix of crypto assets or a broad market index to mirror overall market performance.</p>
             <p>It uses clear, rules-based methods, like market capitalization andtrading activity, to give low-cost exposure to the growth of the crypto market while spreading risk.</p>
             <p>Our research team reviews and adjusts the portfolio each month to keep it aligned with market trends and investment goals.</p>`,
      image: null,
      backgroundColor: 'var(--card-color)',      // Use card color
      link: '/funds#passive',
      infoLink: '/learn?topic=passive'
    },
    {
      title: 'Managed Funds',
      desc: `<p>Smart beta funds use algorithmic strategies to select and weight crypto assets based on factors like momentum or volatility, aiming to outperform the market.</p>
             <p>Special Situations funds focus on unique events that can create short-term opportunities.</p>
             <p>By diversifying across strategies and managers, these funds seek to capture targeted gains while managing risk.</p>`,
      image: null,
      backgroundColor: 'var(--card-color)',      // Use card color
      link: '/funds#smartbeta',
      infoLink: '/learn?topic=smartbeta'
    },
    {
      title: 'GenAI',
      desc: `<p>GenAI funds are built by algorithms powered by generative AI and large language models (LLMs) such as GPT</p>
             <p>Unlike passive index or actively managed funds, they are not ready-made. Each fund is created based on an investor’s chosen goals, risk level, and asset preferences.</p>
             <p>The AI processes market data, news, and blockchain activity to design the strategy and adjusts it as conditions change.</p>`,
      image: null,
      backgroundColor: 'var(--card-color)',      // Use card color
      link: '/funds#special',
      infoLink: '/learn?topic=special'
    }
  ], []);

  const sections = useMemo(() => [
    {
      id: "moolah-capital",
      key: "moolah-capital",
      title: `<span style="color: var(--accent-1)">Simple</span> and <span style="color: var(--accent-1)">Successfull</span> Crypto <span style="color: var(--accent-1)">Investing</span>`,
      description: [
          "Diversified Crypto Investment Solutions for Every Strategy."
      ],
      image: null,
      chartData: fundData[0], // AlphaGlobal Market Index Fund
      link: "/funds",
      linkText: "Explore",
      secondaryLink: "/signup",
      secondaryLinkText: "Get Started",
      reverse: false,
      backgroundColor: COLORS.MEDIUM_BLUE, // Medium blue background
      textColor: COLORS.TEXT_BLACK, // Dark text for light background
      bubbles: true, // Enable floating bubbles
      renderContent: (section) => (
        <div className="row align-items-center">
          <div className="col-lg-6 text-center text-center-mbl order-lg-2 order-1 mb-4 mb-lg-0" style={{
            paddingLeft: 'var(--section-half-padding)',
            paddingRight: 'var(--section-content-padding)'
          }}>
            {section.chartData && (
              <Link to={section.link} className="text-decoration-none">
                <div style={{
                  backgroundColor: 'transparent',
                  maxWidth: 'var(--chart-width-large)',
                  margin: '0 auto',
                  border: 'none',
                  borderRadius: 'var(--border-radius-md)',
                  boxShadow: 'none',
                  padding: 'var(--container-padding-x)',
                  cursor: 'pointer',
                  transition: 'transform 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'scale(1.02)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'scale(1)';
                }}>
                <div style={{ position: 'relative', height: 'var(--chart-height-default)', width: '100%' }}>
                  <div style={{
                    position: 'absolute',
                    top: 'var(--chart-legend-top)',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    display: 'flex',
                    gap: 'var(--spacing-md)',
                    fontSize: 'var(--font-size-xs)',
                    zIndex: 10
                  }}>
                    <span style={{ color: 'var(--text-medium-blue)' }}>
                      <span style={{ color: COLORS.RED }}>●</span> Moolah Funds
                    </span>
                    <span style={{ color: 'var(--text-medium-blue)' }}>
                      <span style={{ color: COLORS.FRAME2_COLOR }}>●</span> Market Index
                    </span>
                  </div>
                  <svg width="100%" height="100%" viewBox="0 0 1200 600" style={{ overflow: 'visible', marginTop: 'var(--container-padding-x)' }}>
                    <defs>
                      <pattern id="grid" width="120" height="80" patternUnits="userSpaceOnUse">
                        <path d="M 120 0 L 0 0 0 80" fill="none" stroke="#e0e0e0" strokeWidth="0.5" opacity="0.2"/>
                      </pattern>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#grid)" />
                    <text x="40" y="520" fill="var(--text-medium-blue)" fontSize="var(--font-size-lg)" textAnchor="start" opacity="0.5">0%</text>
                    <text x="40" y="350" fill="var(--text-medium-blue)" fontSize="var(--font-size-lg)" textAnchor="start" opacity="0.5">30%</text>
                    <text x="40" y="150" fill="var(--text-medium-blue)" fontSize="var(--font-size-lg)" textAnchor="start" opacity="0.5">70%</text>
                    <text x="250" y="560" fill="var(--text-medium-blue)" fontSize="var(--font-size-lg)" textAnchor="middle" opacity="0.5">2022</text>
                    <text x="600" y="560" fill="var(--text-medium-blue)" fontSize="var(--font-size-lg)" textAnchor="middle" opacity="0.5">2023</text>
                    <text x="950" y="560" fill="var(--text-medium-blue)" fontSize="var(--font-size-lg)" textAnchor="middle" opacity="0.5">2024</text>
                    <path d="M 250 480 L 600 320 L 950 180" fill="none" stroke={COLORS.RED} strokeWidth="6" strokeLinecap="round" opacity="0.3" />
                    <path d="M 250 500 L 600 380 L 950 250" fill="none" stroke={COLORS.FRAME2_COLOR} strokeWidth="6" strokeLinecap="round" opacity="0.3" />
                    <circle cx="250" cy="480" r="8" fill={COLORS.RED} opacity="0.6" />
                    <circle cx="600" cy="320" r="8" fill={COLORS.RED} opacity="0.6" />
                    <circle cx="950" cy="180" r="8" fill={COLORS.RED} opacity="0.6" />
                    <circle cx="250" cy="500" r="8" fill={COLORS.FRAME2_COLOR} opacity="0.6" />
                    <circle cx="600" cy="380" r="8" fill={COLORS.FRAME2_COLOR} opacity="0.6" />
                    <circle cx="950" cy="250" r="8" fill={COLORS.FRAME2_COLOR} opacity="0.6" />
                  </svg>
                </div>
              </div>
              </Link>
            )}
          </div>
          <div className="col-lg-6 text-lg-start text-center order-lg-1 order-2" style={{
            paddingLeft: 'var(--section-content-padding)',
            paddingRight: 'var(--section-half-padding)'
          }}>
            <h2 className="h1-hero" style={{
              fontWeight: '700',
              marginBottom: 'var(--spacing-xl)',
              color: section.textColor || 'inherit'
            }} dangerouslySetInnerHTML={{ __html: section.title }}></h2>
            {section.description && Array.isArray(section.description) && section.description.map((paragraph, idx) => (
              <p key={idx} className="body-text" style={{
                color: section.textColor || 'inherit'
              }} dangerouslySetInnerHTML={{ __html: paragraph }}></p>
            ))}
            {section.link && (
              <div className="text-lg-start text-center">
                <Link role="button" className="btn sec-4-button" style={{
                  marginTop: 'var(--spacing-xl)'
                }} to={section.link}>
                  {section.linkText || "Explore"}
                </Link>
                {section.secondaryLink && (
                  <Link role="button" className="btn sec-4-button" style={{
                    marginLeft: 'var(--spacing-sm)',
                    marginTop: 'var(--spacing-xl)'
                  }} to={section.secondaryLink}>
                    {section.secondaryLinkText || "Get Started"}
                  </Link>
                )}
              </div>
            )}
          </div>
        </div>
      )
    },

    {
      id: 'premium-funds',
      key: 'premium-funds',
      title: 'STEADY GUARD Fund',
      description: [
       "STEADY GUARD follows a passive, market-neutral approach designed to reduce volatility in crypto investing. By balancing long and short positions and focusing on hedged opportunities, it aims to generate steady returns without chasing price swings. Professional oversight ensures execution and counterparty risks are managed so the portfolio remains defensive while still capturing opportunities across markets."
      ],
      image: '/home-easy-safe.jpg',
      chartData: fundData[0], // AlphaGlobal Market Index Fund
      backgroundColor: COLORS.DARK_BLUE, // Medium blue background
      textColor: COLORS.TEXT_WHITE, // Dark text for light background
      bubbles: true, // Enable floating bubbles
      link: "/funds/steady-guard",
      linkText: "Explore",
      reverse: true,
      renderContent: (section) => (
        <div className="row align-items-center">
          <div className={`col-lg-6 text-center text-center-mbl ${section.reverse ? 'order-lg-1' : 'order-lg-2'} order-1 mb-4 mb-lg-0`} style={{
            paddingLeft: section.reverse ? 'var(--section-content-padding)' : 'var(--section-half-padding)',
            paddingRight: section.reverse ? 'var(--section-half-padding)' : 'var(--section-content-padding)'
          }}>
            {section.chartData ? (
              <div style={{
                backgroundColor: 'transparent',
                maxWidth: 'var(--chart-width-small)',
                margin: '0 auto',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                boxShadow: 'none'
              }}>
                <FundChart
                  fundData={section.chartData}
                  compact={false}
                  textColor={section.textColor === COLORS.TEXT_BLACK ? "text-black" : "text-white"}
                  backgroundColor="transparent"
                />
              </div>
            ) : section.image ? (
              <img
                alt={section.title}
                className="img-fluid rounded-lg"
                loading="lazy"
                src={section.image}
                style={{
                  maxWidth: '100%',
                  maxHeight: 'var(--image-height-default)',
                  objectFit: 'cover',
                  width: 'auto'
                }}
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = "/placeholder.jpg";
                }}
              />
            ) : null}
          </div>
          <div className={`col-lg-6 text-lg-start text-center ${section.reverse ? 'order-lg-2' : 'order-lg-1'} order-2`} style={{
            paddingLeft: section.reverse ? 'var(--section-half-padding)' : 'var(--section-content-padding)',
            paddingRight: section.reverse ? 'var(--section-content-padding)' : 'var(--section-half-padding)'
          }}>
            <h2 className="h2" style={{
              marginBottom: 'var(--spacing-lg)',
              color: section.textColor || 'inherit'
            }}>
              {section.title}
            </h2>
            {section.description && Array.isArray(section.description) && section.description.map((paragraph, idx) => (
              <p key={idx} className="body-text" style={{
                color: section.textColor || 'inherit',
                marginBottom: 'var(--spacing-md)'
              }}>
                {paragraph}
              </p>
            ))}
            {section.link && (
              <div className="text-lg-start text-center">
                <Link role="button" className="btn sec-4-button" style={{
                  marginTop: 'var(--spacing-xl)'
                }} to={section.link}>
                  {section.linkText || "Explore"}
                </Link>
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      id: 'smartgrowth-fund',
      key: 'smartgrowth-fund',
      title: 'SMART GROWTH Fund',
      description: [
       "SMART GROWTH combines yield-generating opportunities with value-oriented crypto projects in a smart-beta framework. The portfolio tilts toward assets with sustainable income potential and strong fundamentals, offering investors a structured path to long-term growth. Professional managers monitor yield sources and validate asset quality, keeping the mix diversified and balanced."
      ],
      image: '/home-easy-safe.jpg',
      chartData: fundData[1], // AlphaGlobal Market Index Fund
      backgroundColor: COLORS.MEDIUM_BLUE, // Dark blue background
      textColor: COLORS.TEXT_BLACK, // White text for dark background
      bubbles: true, // Enable floating bubbles
      link: "/funds/smart-growth",
      linkText: "Explore",
      reverse: false,
      renderContent: (section) => (
        <div className="row align-items-center">
          <div className={`col-lg-6 text-center text-center-mbl ${section.reverse ? 'order-lg-1' : 'order-lg-2'} order-1 mb-4 mb-lg-0`} style={{
            paddingLeft: section.reverse ? 'var(--section-content-padding)' : 'var(--section-half-padding)',
            paddingRight: section.reverse ? 'var(--section-half-padding)' : 'var(--section-content-padding)'
          }}>
            {section.chartData ? (
              <div style={{
                backgroundColor: 'transparent',
                maxWidth: 'var(--chart-width-small)',
                margin: '0 auto',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                boxShadow: 'none'
              }}>
                <FundChart
                  fundData={section.chartData}
                  compact={false}
                  textColor={section.textColor === COLORS.TEXT_BLACK ? "text-black" : "text-white"}
                  backgroundColor="transparent"
                />
              </div>
            ) : section.image ? (
              <img
                alt={section.title}
                className="img-fluid rounded-lg"
                loading="lazy"
                src={section.image}
                style={{
                  maxWidth: '100%',
                  maxHeight: 'var(--image-height-default)',
                  objectFit: 'cover',
                  width: 'auto'
                }}
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = "/placeholder.jpg";
                }}
              />
            ) : null}
          </div>
          <div className={`col-lg-6 text-lg-start text-center ${section.reverse ? 'order-lg-2' : 'order-lg-1'} order-2`} style={{
            paddingLeft: section.reverse ? 'var(--section-half-padding)' : 'var(--section-content-padding)',
            paddingRight: section.reverse ? 'var(--section-content-padding)' : 'var(--section-half-padding)'
          }}>
            <h2 className="h2" style={{
              marginBottom: 'var(--spacing-lg)',
              color: section.textColor || 'white'
            }}>
              {section.title}
            </h2>
            {section.description && Array.isArray(section.description) && section.description.map((paragraph, idx) => (
              <p key={idx} className="body-text" style={{
                color: section.textColor || 'white',
                marginBottom: 'var(--spacing-md)'
              }}>
                {paragraph}
              </p>
            ))}
            {section.link && (
              <div className="text-lg-start text-center">
                <Link role="button" className="btn sec-4-button" style={{
                  marginTop: 'var(--spacing-xl)'
                }} to={section.link}>
                  {section.linkText || "Explore"}
                </Link>
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      id: 'premium-funds',
      key: 'premium-funds',
      title: 'TREND EXPLORER Fund',
      description: [
       "TREND EXPLORER is built for investors who want exposure to momentum and emerging opportunities. Its smart-beta core tracks market trends, while an active overlay allocates selectively to high-upside themes such as new protocols or sector narratives. Professional managers validate signals, cap risk, and oversee speculative allocations, blending systematic momentum with expert judgment."
      ],
      image: '/home-easy-safe.jpg',
      chartData: fundData[2], // AlphaGlobal Market Index Fund
      backgroundColor: COLORS.DARK_BLUE, // Medium blue background
      textColor: COLORS.TEXT_WHITE, // Dark text for light background
      bubbles: true, // Enable floating bubbles
      link: "/funds/trend-explorer",
      linkText: "Explore",
      reverse: true,
      renderContent: (section) => (
        <div className="row align-items-center">
          <div className={`col-lg-6 text-center text-center-mbl ${section.reverse ? 'order-lg-1' : 'order-lg-2'} order-1 mb-4 mb-lg-0`} style={{
            paddingLeft: section.reverse ? 'var(--section-content-padding)' : 'var(--section-half-padding)',
            paddingRight: section.reverse ? 'var(--section-half-padding)' : 'var(--section-content-padding)'
          }}>
            {section.chartData ? (
              <div style={{
                backgroundColor: 'transparent',
                maxWidth: 'var(--chart-width-small)',
                margin: '0 auto',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                boxShadow: 'none'
              }}>
                <FundChart
                  fundData={section.chartData}
                  compact={false}
                  textColor={section.textColor === COLORS.TEXT_BLACK ? "text-black" : "text-white"}
                  backgroundColor="transparent"
                />
              </div>
            ) : section.image ? (
              <img
                alt={section.title}
                className="img-fluid rounded-lg"
                loading="lazy"
                src={section.image}
                style={{
                  maxWidth: '100%',
                  maxHeight: 'var(--image-height-default)',
                  objectFit: 'cover',
                  width: 'auto'
                }}
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = "/placeholder.jpg";
                }}
              />
            ) : null}
          </div>
          <div className={`col-lg-6 text-lg-start text-center ${section.reverse ? 'order-lg-2' : 'order-lg-1'} order-2`} style={{
            paddingLeft: section.reverse ? 'var(--section-half-padding)' : 'var(--section-content-padding)',
            paddingRight: section.reverse ? 'var(--section-content-padding)' : 'var(--section-half-padding)'
          }}>
            <h2 className="h2" style={{
              marginBottom: 'var(--spacing-lg)',
              color: section.textColor || 'inherit'
            }}>
              {section.title}
            </h2>
            {section.description && Array.isArray(section.description) && section.description.map((paragraph, idx) => (
              <p key={idx} className="body-text" style={{
                color: section.textColor || 'inherit',
                marginBottom: 'var(--spacing-md)'
              }}>
                {paragraph}
              </p>
            ))}
            {section.link && (
              <div className="text-lg-start text-center">
                <Link role="button" className="btn sec-4-button" style={{
                  marginTop: 'var(--spacing-xl)'
                }} to={section.link}>
                  {section.linkText || "Explore"}
                </Link>
              </div>
            )}
          </div>
        </div>
      )
    },
     {
      id: 'smartgrowth-fund',
      key: 'smartgrowth-fund',
      title: 'GENERATIVE AI Fund',
      description: [
       "GENERATIVE AI creates personalized portfolios through an interactive process that learns each investor’s goals and risk profile. Trained on domain-specific data for index investing, it generates tailored allocations, asset mixes, and rebalancing strategies. Every GenAI portfolio is reviewed by professional managers, ensuring AI-driven customization is matched with oversight and risk control."
      ],
      image: '/home-easy-safe.jpg',
      chartData: fundData[3], // AlphaGlobal Market Index Fund
      backgroundColor: COLORS.MEDIUM_BLUE, // Dark blue background
      textColor: COLORS.TEXT_BLACK, // White text for dark background
      bubbles: true, // Enable floating bubbles
      link: "/funds/generative-ai",
      linkText: "Explore",
      reverse: false,
      renderContent: (section) => (
        <div className="row align-items-center">
          <div className={`col-lg-6 text-center text-center-mbl ${section.reverse ? 'order-lg-1' : 'order-lg-2'} order-1 mb-4 mb-lg-0`} style={{
            paddingLeft: section.reverse ? 'var(--section-content-padding)' : 'var(--section-half-padding)',
            paddingRight: section.reverse ? 'var(--section-half-padding)' : 'var(--section-content-padding)'
          }}>
            {section.chartData ? (
              <div style={{
                backgroundColor: 'transparent',
                maxWidth: 'var(--chart-width-small)',
                margin: '0 auto',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                boxShadow: 'none'
              }}>
                <FundChart
                  fundData={section.chartData}
                  compact={false}
                  textColor={section.textColor === COLORS.TEXT_BLACK ? "text-black" : "text-white"}
                  backgroundColor="transparent"
                />
              </div>
            ) : section.image ? (
              <img
                alt={section.title}
                className="img-fluid rounded-lg"
                loading="lazy"
                src={section.image}
                style={{
                  maxWidth: '100%',
                  maxHeight: 'var(--image-height-default)',
                  objectFit: 'cover',
                  width: 'auto'
                }}
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = "/placeholder.jpg";
                }}
              />
            ) : null}
          </div>
          <div className={`col-lg-6 text-lg-start text-center ${section.reverse ? 'order-lg-2' : 'order-lg-1'} order-2`} style={{
            paddingLeft: section.reverse ? 'var(--section-half-padding)' : 'var(--section-content-padding)',
            paddingRight: section.reverse ? 'var(--section-content-padding)' : 'var(--section-half-padding)'
          }}>
            <h2 className="h2" style={{
              marginBottom: 'var(--spacing-lg)',
              color: section.textColor || 'white'
            }}>
              {section.title}
            </h2>
            {section.description && Array.isArray(section.description) && section.description.map((paragraph, idx) => (
              <p key={idx} className="body-text" style={{
                color: section.textColor || 'white',
                marginBottom: 'var(--spacing-md)'
              }}>
                {paragraph}
              </p>
            ))}
            {section.link && (
              <div className="text-lg-start text-center">
                <Link role="button" className="btn sec-4-button" style={{
                  marginTop: 'var(--spacing-xl)'
                }} to={section.link}>
                  {section.linkText || "Explore"}
                </Link>
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      id: 'moolah-fund-options',
      key: 'moolah-fund-options',
      title: 'Moolah Fund Options',
      isCustomSection: true,
      backgroundColor: COLORS.MEDIUM_BLUE,
      textColor: COLORS.TEXT_BLACK,
      bubbles: true
    },



  ], []);



  return (
    <>
      {/* CSS to eliminate white margins */}
      <style>
        {`
          body, html {
            margin: 0 !important;
            padding: 0 !important;
          }

          footer, .footer, #footer {
            margin-top: 0 !important;
            padding-top: 0 !important;
          }

          .homepage_main {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
          }

          #home-cta {
            margin-bottom: 0 !important;
            padding-bottom: 30px !important;
          }

          .full-width-medium-section {
            margin-bottom: 0 !important;
          }

          /* Target any potential wrapper divs */
          .homepage_main > div:last-child {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
          }

          /* Ensure no gaps between sections and footer */
          section:last-of-type {
            margin-bottom: 0 !important;
          }
        `}
      </style>

      <div className="homepage_main position-relative" style={{
        marginTop: '0',
        marginBottom: '0',
        paddingTop: '0',
        paddingBottom: '0',
        backgroundColor: 'transparent'
      }}>


      {/* Content Container - Starts after full viewport */}
      <div className="page-content-container" style={{
        marginTop: '-100px',
        position: 'relative',
        zIndex: 2
      }}>
        <section id='home-sections' style={{ position: 'relative' }}>

      {sections.map((section) => {
        // Handle custom "Moolah Fund Options" section
        if (section.isCustomSection && section.id === 'moolah-fund-options') {
          return (
            <section key={section.id} style={{
              backgroundColor: COLORS.DARK_BLUE,
              paddingTop: 'var(--container-padding-y)',
              paddingBottom: 'var(--container-padding-y)',
              position: 'relative',
              width: '100%'
            }}>
              {/* Floating bubbles */}
              <div className="fx-bubbles" aria-hidden="true" style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                zIndex: 0,
                pointerEvents: 'none'
              }}>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
              </div>

              <div className="container" style={{ position: 'relative', zIndex: 1, paddingLeft: 'var(--container-padding-x)', paddingRight: 'var(--container-padding-x)' }}>
                {/* Compare Our Fund Strategies Section */}
                <div className="row justify-content-center" style={{ marginBottom: 'var(--spacing-xl)' }}>
                  <div className="col-lg-12 text-center">
                    <h2 className="h2" style={{
                      marginBottom: 'var(--spacing-lg)',
                      color: COLORS.TEXT_WHITE
                    }}>
                      Compare Our Fund Strategies
                    </h2>
                    <p className="body-text" style={{
                      color: COLORS.TEXT_WHITE,
                      marginBottom: 'var(--spacing-xl)',
                      fontSize: 'var(--font-size-lg)'
                    }}>
                      Each fund is designed for different investment goals and risk profiles. Compare our strategies to find the right fit for your portfolio.
                    </p>
                  </div>
                </div>

                <div className="row justify-content-center" style={{ marginBottom: 'var(--spacing-xl)' }}>
                  <div className="col-lg-12">
                    <FundsComparisonTable />
                  </div>
                </div>

                <div className="row justify-content-center" style={{ marginBottom: 'calc(var(--spacing-xl) * 2)' }}>
                  <div className="col-lg-12 text-center">
                    <Link role="button" className="btn sec-4-button" style={{
                      marginTop: 'var(--spacing-xl)'
                    }} to="/funds">
                      Explore All Funds
                    </Link>
                  </div>
                </div>


              </div>
            </section>
          );
        }

        // Simple renderer - no conditional logic, just use section's renderContent
        return (
          <section key={section.id} id={section.id} style={{
            backgroundColor: section.backgroundColor || COLORS.MEDIUM_BLUE,
            paddingTop: section.id === 'moolah-capital' ? 'calc(var(--navbar-height) + var(--hero-padding-top))' : 'var(--container-padding-y)',
            paddingBottom: 'var(--container-padding-y)',
            paddingLeft: '0',
            paddingRight: '0',
            marginTop: section.id === 'moolah-capital' ? '-1px' : '0',
            position: 'relative'
          }}>
            {section.bubbles && (
              <div className="fx-bubbles" aria-hidden="true">
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
              </div>
            )}
            <div className={`container ${section.backgroundColor === COLORS.DARK_BLUE ? 'text-white' : 'text-black'}`} style={{
              paddingLeft: 'var(--container-padding-x)',
              paddingRight: 'var(--container-padding-x)',
              position: 'relative',
              zIndex: 1
            }}>
              {section.renderContent ? section.renderContent(section) : (
                <div className="row align-items-center">
                  <div className="col-lg-12 text-center">
                    <h2 className="h2" style={{ marginBottom: 'var(--spacing-lg)' }}>
                      {section.title}
                    </h2>
                    <p>No renderContent function defined for {section.id}</p>
                  </div>
                </div>
              )}
            </div>
          </section>
        );

      })}
      </section>
      </div>
    </div>
    </>
  );
};

export default Funds;
