import React from 'react';
import { COLORS } from '../../constants/colors';

export default function FundsComparisonTable() {
  const funds = [
    {
      name: "STEADY GUARD",
      strategy: "Market Neutral",
      type: "Passive",
      focus: "Hedged positions and arbitrage to reduce volatility",
      appeal: "Seeks steady growth in any market with a defensive approach",
    },
    {
      name: "SMART GROWTH",
      strategy: "Yield + Value",
      type: "Smart Beta",
      focus: "Blends staking/lending income with value-driven projects",
      appeal: "Balanced, diversified path to sustainable long-term growth",
    },
    {
      name: "TREND EXPLORER",
      strategy: "Momentum + Speculative",
      type: "Smart Beta + Active",
      focus: "Momentum-based core with selective high-upside opportunities",
      appeal: "Captures market trends while exploring new opportunities",
    },
    {
      name: "GEN AI",
      strategy: "Custom ETF",
      type: "Active",
      focus: "Personalized allocations based on investor goals and risk",
      appeal: "Your own tailored crypto index fund with AI + manager oversight",
    },
  ];

  return (
    <div style={{
      overflowX: 'auto',
      borderRadius: 'var(--border-radius-md)',
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
    }}>
      <table style={{
        width: '100%',
        borderCollapse: 'collapse',
        fontSize: '0.9rem',
        backgroundColor: 'transparent'
      }}>
        <thead style={{
          backgroundColor: COLORS.BUTTON_RED,
          borderBottom: '2px solid rgba(255, 255, 255, 0.3)'
        }}>
          <tr>
            <th style={{ 
              padding: 'var(--spacing-md) var(--spacing-sm)',
              textAlign: 'center',
              fontWeight: '600',
              color: COLORS.WHITE,
              borderRight: '2px solid rgba(255, 255, 255, 0.3)',
              fontSize: '0.85rem'
            }}>FUND</th>
            <th style={{ 
              padding: 'var(--spacing-md) var(--spacing-sm)',
              textAlign: 'center',
              fontWeight: '600',
              color: COLORS.WHITE,
              borderRight: '2px solid rgba(255, 255, 255, 0.3)',
              fontSize: '0.85rem'
            }}>STRATEGY</th>
            <th style={{
              padding: 'var(--spacing-md) var(--spacing-sm)',
              textAlign: 'center',
              fontWeight: '600',
              color: COLORS.WHITE,
              borderRight: '2px solid rgba(255, 255, 255, 0.3)',
              fontSize: '0.85rem'
            }}>INVESTMENT TYPE</th>
            <th style={{
              padding: 'var(--spacing-md) var(--spacing-sm)',
              textAlign: 'center',
              fontWeight: '600',
              color: COLORS.WHITE,
              borderRight: '2px solid rgba(255, 255, 255, 0.3)',
              fontSize: '0.85rem'
            }}>FOCUS</th>
            <th style={{ 
              padding: 'var(--spacing-md) var(--spacing-sm)',
              textAlign: 'center',
              fontWeight: '600',
              color: COLORS.WHITE,
              fontSize: '0.85rem'
            }}>GOAL</th>
          </tr>
        </thead>
        <tbody>
          {funds.map((fund, index) => (
            <tr key={fund.name} style={{
              backgroundColor: index % 2 === 0 ? 'rgba(255, 255, 255, 0.05)' : 'rgba(255, 255, 255, 0.1)',
              borderBottom: '2px solid rgba(255, 255, 255, 0.3)'
            }}>
              <td style={{
                padding: 'var(--spacing-md) var(--spacing-sm)',
                fontWeight: '500',
                color: COLORS.WHITE,
                backgroundColor: COLORS.BUTTON_RED,
                borderRight: '2px solid rgba(255, 255, 255, 0.3)'
              }}>{fund.name}</td>
              <td style={{
                padding: 'var(--spacing-md) var(--spacing-sm)',
                color: COLORS.TEXT_WHITE,
                borderRight: '2px solid rgba(255, 255, 255, 0.3)'
              }}>{fund.strategy}</td>
              <td style={{
                padding: 'var(--spacing-md) var(--spacing-sm)',
                color: COLORS.TEXT_WHITE,
                borderRight: '2px solid rgba(255, 255, 255, 0.3)'
              }}>{fund.type}</td>
              <td style={{
                padding: 'var(--spacing-md) var(--spacing-sm)',
                color: COLORS.TEXT_WHITE,
                borderRight: '2px solid rgba(255, 255, 255, 0.3)'
              }}>{fund.focus}</td>
              <td style={{
                padding: 'var(--spacing-md) var(--spacing-sm)',
                color: COLORS.TEXT_WHITE
              }}>{fund.appeal}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
