import { useParams } from 'react-router-dom';
import FundPage from '../Fund/FundPage.jsx';

const FundsDetail = () => {
  const { fundId } = useParams();  // Get the fundId, which will include 'funds-'

  // Map fund URLs to fund IDs in funds.json (now they match!)
  const fundData = [
    { id: 'steady-guard', component: <FundPage fund="steady-guard" /> },
    { id: 'smart-growth', component: <FundPage fund="smart-growth" /> },
    { id: 'trend-explorer', component: <FundPage fund="trend-explorer" /> },
    { id: 'generative-ai', component: <FundPage fund="generative-ai" /> },
  ];

  // Find the fund that matches the fundId
  const fund = fundData.find(fund => fund.id === fundId);

  // If no fund matches, show a "not found" message
  if (!fund) {
    return <div className='text-black py-5 my-5 display-4 fw-bold'>Fund not found</div>;
  }

  return (
    <>
    {fund.component}
    </>
  );
}

export default FundsDetail;
