import React from 'react';
import { Link } from 'react-router-dom';
import { useContent } from '../../context/ContentContext';

const FundsListNew = () => {
  // Use the content context to get funds data
  const { content } = useContent();
  const { funds } = content;

  return (
    <div className="container py-5">
      <div className="row">
        <div className="col-12 mb-5">
          <h2 className="display-4 fw-bold text-center">Our Funds</h2>
          <p className="text-center body-text">
            Explore our range of professionally managed crypto funds designed to meet different investment objectives.
          </p>
        </div>
      </div>

      <div className="row g-4">
        {funds.map((fund) => (
          <div className="col-md-6 col-lg-3" key={fund.id}>
            <div className="card h-100 funds-card">
              <div className="card-body">
                <h4 className="card-title mb-3">{fund.name}</h4>
                <p className="card-text body-text-small">{fund.shortDescription}</p>
                <ul className="list-unstyled mt-4">
                  {fund.features.map((feature, index) => (
                    <li className="mb-2" key={index}>
                      <i className="bi bi-check-circle-fill text-success me-2" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
              <div className="card-footer bg-transparent border-0 pb-4">
                <Link className="btn btn-outline-primary rounded-pill px-4" to={`/funds/${fund.id}`}>
                  Learn More
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FundsListNew;
