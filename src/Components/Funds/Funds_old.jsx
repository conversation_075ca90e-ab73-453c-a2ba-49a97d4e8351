// Funds.jsx

import React, { useEffect } from 'react';
import { useLocation, Link } from 'react-router-dom';
import CTASection from '../../App/CTA/CTASection.jsx';
import FundChart from '../Homepage/FundChart.jsx';

// Import global colors
import { COLORS } from '../../constants/colors';

// Use global colors directly
const FUNDS_COLORS = COLORS;

const Funds = () => {
  const routerLocation = useLocation();

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Fund data for Diversified Portfolio chart
  const fundData = [
    {
      name: "Market Index Fund",
      performance: 85,
      risk: 30,
      fees: 95,
      liquidity: 90
    },
    {
      name: "Momentum Fund",
      performance: 78,
      risk: 65,
      fees: 70,
      liquidity: 85
    },
    {
      name: "DeFi Fund",
      performance: 82,
      risk: 45,
      fees: 80,
      liquidity: 88
    },
    {
      name: "Income Fund",
      performance: 87,
      risk: 47,
      fees: 75,
      liquidity: 83
    },
    {
      name: "GenAI Fund",
      performance: 85,
      risk: 48,
      fees: 76,
      liquidity: 85
    },
    {
      name: "Moolah Capital Portfolio",
      performance: 90,
      risk: 48,
      fees: 74,
      liquidity: 82
    }
  ];



  // Improved hash navigation handling
  useEffect(() => {
    // First scroll to top to ensure consistent positioning
    window.scrollTo(0, 0);

    // Then handle hash navigation with a slight delay to ensure DOM is ready
    if (routerLocation.hash) {
      // Use setTimeout to ensure the component is fully rendered
      setTimeout(() => {
        const id = routerLocation.hash.slice(1); // Remove the # character
        const element = document.getElementById(id);

        if (element) {
          // Calculate position with further adjusted navbar offset
          const navbarHeight = 80; // Reduced from 90px to 80px
          const elementPosition = element.getBoundingClientRect().top;
          const offsetPosition = elementPosition + window.pageYOffset - navbarHeight;

          // Scroll to the element
          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          });
        } else {
          console.warn(`Element with id "${id}" not found`);
        }
      }, 300);
    }
  }, [routerLocation]);

  return (
    <>
      {/* CSS to eliminate white margins */}
      <style>
        {`
          .funds_main {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
          }

          #funds-cta {
            margin-bottom: 0 !important;
          }

          .full-width-dark-section, .full-width-medium-section {
            margin-top: 0 !important;
            margin-bottom: 0 !important;
          }

          section {
            margin-top: 0 !important;
            margin-bottom: 0 !important;
          }

          .page-content-container {
            margin-top: 0 !important;
            margin-bottom: 0 !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
          }
        `}
      </style>

      <div>
      {/* Section 1 - Hero */}
      <section style={{
        backgroundColor: COLORS.DARK_BLUE,
        minHeight: '70vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative'
      }}>
        {/* Floating bubbles for hero section */}
        <div className="fx-bubbles" aria-hidden="true" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 0,
          pointerEvents: 'none'
        }}>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
        </div>

        <div className="container text-center" style={{ position: 'relative', zIndex: 1 }}>
          <h1 className="display-4 text-white mb-4 mobile-header">Our Investment Funds</h1>
          <p className="lead text-white mb-5">Diversified crypto investment solutions for every strategy</p>
        </div>
      </section>

      {/* Section - We fuse index investing with GenAI */}
      <section className="full-width-medium-section" style={{
        backgroundColor: COLORS.MEDIUM_BLUE,
        paddingTop: 'var(--section-padding-y)',
        paddingBottom: 'var(--section-padding-y)',
        paddingLeft: '0',
        paddingRight: '0',
        position: 'relative'
      }}>
        {/* Floating bubbles */}
        <div className="fx-bubbles" aria-hidden="true" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '100%',
          zIndex: 0,
          pointerEvents: 'none'
        }}>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
        </div>

        <div className={`container text-black`} style={{
          fontWeight: 'normal',
          paddingLeft: '20px',
          paddingRight: '20px'
        }}>
          <div className="row align-items-center">
            <div className="col-lg-5 text-center text-center-mbl order-lg-2 order-1 mb-4 mb-lg-0">
              {/* Custom performance chart */}
              <Link to="/funds" onClick={scrollToTop} style={{ textDecoration: 'none' }}>
                <div style={{
                  backgroundColor: 'transparent',
                  maxWidth: '1200px',
                  margin: '0 auto',
                  border: 'none',
                  borderRadius: '0.5rem',
                  boxShadow: 'none',
                  padding: '20px',
                  cursor: 'pointer',
                  transition: 'transform 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'scale(1.02)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'scale(1)';
                }}>
                  <div style={{ position: 'relative', height: '600px', width: '100%' }}>
                    {/* Legend - top middle, one line, smaller font */}
                    <div style={{
                      position: 'absolute',
                      top: '120px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      display: 'flex',
                      gap: '15px',
                      fontSize: '12px',
                      zIndex: 10
                    }}>
                      <span style={{ color: 'var(--text-medium-blue)' }}>
                        <span style={{ color: COLORS.RED }}>●</span> Moolah Funds
                      </span>
                      <span style={{ color: 'var(--text-medium-blue)' }}>
                        <span style={{ color: COLORS.FRAME2_COLOR }}>●</span> Market Index
                      </span>
                    </div>

                    <svg width="100%" height="100%" viewBox="0 0 1200 600" style={{ overflow: 'visible', marginTop: '20px' }}>
                      {/* Grid lines */}
                      <defs>
                        <pattern id="grid" width="120" height="80" patternUnits="userSpaceOnUse">
                          <path d="M 120 0 L 0 0 0 80" fill="none" stroke="#e0e0e0" strokeWidth="0.5" opacity="0.2"/>
                        </pattern>
                      </defs>
                      <rect width="100%" height="100%" fill="url(#grid)" />

                      {/* Y-axis labels - 0%, 30%, 70% - more transparent */}
                      <text x="40" y="520" fill="var(--text-medium-blue)" fontSize="18" textAnchor="start" opacity="0.5">0%</text>
                      <text x="40" y="350" fill="var(--text-medium-blue)" fontSize="18" textAnchor="start" opacity="0.5">30%</text>
                      <text x="40" y="150" fill="var(--text-medium-blue)" fontSize="18" textAnchor="start" opacity="0.5">70%</text>

                      {/* X-axis labels - 2022, 2023, 2024 - more transparent */}
                      <text x="250" y="560" fill="var(--text-medium-blue)" fontSize="18" textAnchor="middle" opacity="0.5">2022</text>
                      <text x="600" y="560" fill="var(--text-medium-blue)" fontSize="18" textAnchor="middle" opacity="0.5">2023</text>
                      <text x="950" y="560" fill="var(--text-medium-blue)" fontSize="18" textAnchor="middle" opacity="0.5">2024</text>

                      {/* Performance line 1 - RED (very transparent) */}
                      <path
                        d="M 250 480 L 600 320 L 950 180"
                        fill="none"
                        stroke={COLORS.RED}
                        strokeWidth="6"
                        strokeLinecap="round"
                        opacity="0.3"
                      />

                      {/* Performance line 2 - FRAME2_COLOR (very transparent) */}
                      <path
                        d="M 250 500 L 600 380 L 950 250"
                        fill="none"
                        stroke={COLORS.FRAME2_COLOR}
                        strokeWidth="6"
                        strokeLinecap="round"
                        opacity="0.3"
                      />

                      {/* Data points for line 1 */}
                      <circle cx="250" cy="480" r="8" fill={COLORS.RED} opacity="0.6" />
                      <circle cx="600" cy="320" r="8" fill={COLORS.RED} opacity="0.6" />
                      <circle cx="950" cy="180" r="8" fill={COLORS.RED} opacity="0.6" />

                      {/* Data points for line 2 */}
                      <circle cx="250" cy="500" r="8" fill={COLORS.FRAME2_COLOR} opacity="0.6" />
                      <circle cx="600" cy="380" r="8" fill={COLORS.FRAME2_COLOR} opacity="0.6" />
                      <circle cx="950" cy="250" r="8" fill={COLORS.FRAME2_COLOR} opacity="0.6" />
                    </svg>
                  </div>
                </div>
              </Link>
            </div>
            <div className="col-lg-7 order-lg-1 order-2">
              <h1 className="h1 mb-0" style={{ color: COLORS.TEXT_BLACK }}>
                Simple and Succesfull Crypto Investments: Moolah Funds
              </h1>
              <div className="text-lg-start text-center">
                <Link role="button" className="btn sec-4-button ms-lg-4 mt-4" onClick={scrollToTop} to="/funds">
                  Explore
                </Link>
                <Link role="button" className="btn sec-4-button ms-lg-2 mt-4" onClick={scrollToTop} to="/signup">
                  Get Started
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 2 - Funds Intro */}
      <section className="full-width-dark-section" style={{ backgroundColor: FUNDS_COLORS.DARK_BLUE, minHeight: '500px', paddingTop: '40px', paddingBottom: '40px', paddingLeft: '0', paddingRight: '0', display: 'flex', alignItems: 'center', position: 'relative' }}>
          {/* Floating bubbles for Moolah Capital Passive Funds section */}
          <div className="fx-bubbles" aria-hidden="true" style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 0,
            pointerEvents: 'none'
          }}>
            <span className="b"></span>
            <span className="b"></span>
            <span className="b"></span>
            <span className="b"></span>
            <span className="b"></span>
          </div>
        <div className="container text-white" style={{ paddingLeft: 'var(--container-padding-x-large)', paddingRight: 'var(--container-padding-x-large)', position: 'relative', zIndex: 1 }}>
          <div className='row'>
            <div className='col-lg-6 my-3'>
              <h2 className='h2 text-start mb-4 mobile-header' style={{ marginTop: '0' }}>Moolah Capital Passive Funds</h2>
              <p className='body-text text-start'>Passive index funds aim to match the performance of a market index — like the S&P 500 or a crypto market index — instead of trying to beat it. They do this by holding the same assets, in the same proportions, as the index they follow.</p>
              <p className='body-text text-start'>The <strong>Moolah Capital AlphaGlobal Market Index Fund</strong> is Moolah Capital's fund in this category, giving investors simple and cost-effective exposure to the overall crypto market.</p>
              <p className='body-text text-start'>These funds are:</p>
              <ul className='body-text text-start'>
                <li><span style={{ fontWeight: 'bold' }}>Low cost</span> – minimal trading and no active management keep fees down.</li>
                <li><span style={{ fontWeight: 'bold' }}>Diversified</span> – tracking a broad index spreads risk across many assets.</li>
                <li><span style={{ fontWeight: 'bold' }}>Transparent</span> – holdings closely match a publicly available index.</li>
                <li><span style={{ fontWeight: 'bold' }}>Consistent</span> – performance moves in line with the market over time.</li>
              </ul>
            </div>
            <div className='col-lg-6 my-3 d-flex justify-content-center align-items-center' style={{ padding: 'var(--container-padding-x)' }}>
              <div style={{
                backgroundColor: 'transparent',
                maxWidth: '400px',
                width: '100%',
                margin: '0 auto',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                boxShadow: 'none'
              }}>
                <FundChart
                  fundData={fundData[0]}
                  compact={false}
                  textColor='text-white'
                  backgroundColor="transparent"
                />
              </div>
            </div>
          </div>
          {/* CTA Section - Full Width */}
          <div className="d-flex justify-content-center align-items-center my-4 text-center-mbl" style={{ minHeight: '150px', width: '100%' }}>
            <div className="d-flex flex-row gap-4 justify-content-center" style={{ width: '100%' }}>
              <CTASection
                title="Start Your Financial Journey"
                cta="Sign Up"
                link="/signup#account-signup"
                theme='signup'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-dark-section"
              />
              <CTASection
                title="Request Info about this Fund"
                cta="Learn More"
                link="/learn?topic=smartbeta"
                theme='learn'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-dark-section"
              />
            </div>
          </div>
        </div>
      </section>

         {/* Section 5 - Smart Beta Funds */}
         <section id="smartbeta" className='position-relative overflow-hidden full-width-medium-section' style={{ backgroundColor: FUNDS_COLORS.MEDIUM_BLUE, minHeight: '500px', paddingTop: '40px', paddingBottom: '40px', display: 'flex', alignItems: 'center', color: 'var(--text-medium-blue)', marginTop: '0' }}>
          {/* Floating bubbles for Smart Beta Funds section */}
          <div className="fx-bubbles" aria-hidden="true" style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '100%',
            zIndex: 0,
            pointerEvents: 'none'
          }}>
            <span className="b"></span>
            <span className="b"></span>
            <span className="b"></span>
            <span className="b"></span>
            <span className="b"></span>
          </div>

        <div className="container" style={{ color: 'var(--text-medium-blue)', paddingLeft: 'var(--container-padding-x)', paddingRight: 'var(--container-padding-x)', position: 'relative', zIndex: 1 }}>
          <div className='row flex-lg-row-reverse'>
            <div className='col-lg-6' style={{ paddingTop: '40px' }}>
              <h2 className='h2 text-start mb-4 mobile-header' style={{ marginTop: '0' }}>Moolah Capital Smart Beta Funds</h2>
              <p className='body-text text-start'>Smart beta funds use clear, rules-based methods to choose and weight assets based on factors that may improve returns or lower risk — such as liquidity, volatility, on-chain activity, or network growth.</p>
              <p className='body-text text-start'>The <strong>Moolah Capital AlphaGlobal Momentum Fund</strong> is Moolah Capital’s smart beta fund in this category, focusing on assets with strong upward trends.</p>
              <p className='body-text text-start'>These strategies aim to beat traditional indices or offer better risk-adjusted returns, while keeping costs lower than active funds, though slightly higher than standard passive funds.</p>
              <p className='body-text text-start'>Our smart beta crypto funds publish their rules for full transparency and apply filters to help reduce downside risk.</p>
            </div>
            <div className='col-lg-6 my-3 d-flex justify-content-center align-items-center' style={{ padding: 'var(--container-padding-x)' }}>
              <div style={{
                backgroundColor: 'transparent',
                maxWidth: '400px',
                width: '100%',
                margin: '0 auto',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                boxShadow: 'none'
              }}>
                <FundChart
                  fundData={fundData[1]}
                  compact={false}
                  textColor='text-dark'
                  backgroundColor="transparent"
                />
              </div>
            </div>
          </div>
          {/* CTA Section - Full Width */}
          <div className="d-flex justify-content-center align-items-center my-4 text-center-mbl" style={{ minHeight: '150px', width: '100%' }}>
            <div className="d-flex flex-row gap-4 justify-content-center" style={{ width: '100%' }}>
              <CTASection
                title="Start Your Financial Journey"
                cta="Sign Up"
                link="/signup#account-signup"
                theme='signup'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-blue-section"
                titleStyle={{ color: 'var(--text-medium-blue)' }}
              />
              <CTASection
                title="Request Info about this Fund"
                cta="Learn More"
                link="/learn?topic=smartbeta"
                theme='learn'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-blue-section"
                titleStyle={{ color: 'var(--text-medium-blue)' }}
              />
            </div>
          </div>
        </div>
      </section>

      <section id="funds-intro" className='position-relative text-white overflow-hidden full-width-dark-section' style={{ backgroundColor: FUNDS_COLORS.DARK_BLUE, minHeight: '500px', paddingTop: '40px', paddingBottom: '40px', paddingLeft: '0', paddingRight: '0', display: 'flex', alignItems: 'center' }}>
        {/* Floating bubbles for DeFi Funds section */}
        <div className="fx-bubbles" aria-hidden="true" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 0,
          pointerEvents: 'none'
        }}>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
        </div>

        <div className="container text-white" style={{ paddingLeft: 'var(--container-padding-x-large)', paddingRight: 'var(--container-padding-x-large)', position: 'relative', zIndex: 1 }}>
          <div className='row'>
            <div className='col-lg-6 my-3'>
              <h2 className='h2 text-start mb-4 mobile-header' style={{ marginTop: '0' }}>Moolah Capital DeFi Funds</h2>
              <p className='body-text text-start'>The <strong>AlphaGlobal DeFi Leaders Fund</strong> provides strategic exposure to the most innovative and resilient projects in the decentralised finance ecosystem. Our investment approach focuses on established DeFi protocols with:</p>
              <ul className='body-text text-start'>
               <li><span style={{ fontWeight: 'bold' }}>Strong </span> market leadership in lending, trading, derivatives, and liquidity solutions.</li>
               <li><span style={{ fontWeight: 'bold' }}>Robust</span> security frameworks with audited smart contracts and proven operational resilience.</li>
               <li><span style={{ fontWeight: 'bold' }}>Sustainable</span> tokenomics and revenue-generating business models.</li>
               <li><span style={{ fontWeight: 'bold' }}>High</span> user adoption and network activity, signalling long-term growth potential.</li>
              </ul>
              <p className='body-text text-start'>DeFi has emerged as one of the fastest-growing segments in digital assets, reshaping how individuals and institutions access financial services. By concentrating on industry leaders, the fund aims to deliver diversified exposure to the sector while managing the inherent risks of emerging technologies.</p>
            </div>
            <div className='col-lg-6 my-3 d-flex justify-content-center align-items-center' style={{ padding: 'var(--container-padding-x-large)' }}>
              <div style={{
                backgroundColor: 'transparent',
                maxWidth: '400px',
                width: '100%',
                margin: '0 auto',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                boxShadow: 'none'
              }}>
                <FundChart
                  fundData={fundData[2]}
                  compact={false}
                  textColor='text-white'
                  backgroundColor="transparent"
                />
              </div>
            </div>
          </div>
          {/* CTA Section - Full Width */}
          <div className="d-flex justify-content-center align-items-center my-4 text-center-mbl" style={{ minHeight: '150px', width: '100%' }}>
            <div className="d-flex flex-row gap-4 justify-content-center" style={{ width: '100%' }}>
              <CTASection
                title="Start Your Financial Journey"
                cta="Sign Up"
                link="/signup#account-signup"
                theme='signup'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-dark-section"
              />
              <CTASection
                title="Request Info about this Fund"
                cta="Learn More"
                link="/learn?topic=smartbeta"
                theme='learn'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-dark-section"
              />
            </div>
          </div>
        </div>
      </section>

          {/* Section 5 - Special Situations Funds */}
      <section id="special" className='position-relative overflow-hidden full-width-medium-section' style={{ backgroundColor: FUNDS_COLORS.MEDIUM_BLUE, minHeight: '500px', paddingTop: '40px', paddingBottom: '40px', paddingLeft: '0', paddingRight: '0', display: 'flex', alignItems: 'center', color: 'var(--text-medium-blue)' }}>
          {/* Floating bubbles for Special Situations Funds section */}
          <div className="fx-bubbles" aria-hidden="true" style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '100%',
            zIndex: 0,
            pointerEvents: 'none'
          }}>
            <span className="b"></span>
            <span className="b"></span>
            <span className="b"></span>
            <span className="b"></span>
            <span className="b"></span>
          </div>

        <div className="container" style={{ color: 'var(--text-medium-blue)', paddingLeft: 'var(--container-padding-x)', paddingRight: 'var(--container-padding-x)', position: 'relative', zIndex: 1 }}>
          <div className='row flex-lg-row-reverse'>
            <div className='col-lg-6' style={{ paddingTop: '40px' }}>
              <h2 className='h2 text-start mb-4 mobile-header' style={{ marginTop: '0' }}>Moolah Capital Special Situations Funds</h2>
              <p className='body-text text-start'>The <strong>Moolah Capital AlphaGlobal Special Situations Fund</strong> is event-driven and opportunistic, focusing on tactical, time-sensitive trades. Positions are typically held only for the duration of a particular event or until the value is realised.</p>
                <p className='body-text text-start'>Examples of opportunities include:</p>
                <ul className='body-text text-start'>
                 <li><span style={{ fontWeight: 'bold' }}>Token</span> restructurings or governance transitions.</li>
                 <li><span style={{ fontWeight: 'bold' }}>Distressed</span> projects with recovery potential.</li>
                 <li><span style={{ fontWeight: 'bold' }}>Token</span> unlock events or blockchain forks.</li>
                 <li><span style={{ fontWeight: 'bold' }}>Liquidity</span> provisioning linked to protocol upgrades.</li>
                 <li><span style={{ fontWeight: 'bold' }}>Litigation-driven</span> trades or quantitative arbitrage.</li>
                  <li><span style={{ fontWeight: 'bold' }}>DeFi yield</span> strategies or selective early-stage token venture.</li>
                </ul>
                <p className='body-text text-start'>By pursuing targeted opportunities with asymmetric upside potential, the Special Situations Fund aims to deliver returns that move independently from broader crypto market trends.</p>
            </div>
            <div className='col-lg-6 my-3 d-flex justify-content-center align-items-center' style={{ padding: 'var(--container-padding-x)' }}>
              <div style={{
                backgroundColor: 'transparent',
                maxWidth: '400px',
                width: '100%',
                margin: '0 auto',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                boxShadow: 'none'
              }}>
                <FundChart
                  fundData={fundData[3]}
                  compact={false}
                  textColor='text-dark'
                  backgroundColor="transparent"
                />
              </div>
            </div>
          </div>
          {/* CTA Section - Full Width */}
          <div className="d-flex justify-content-center align-items-center my-4 text-center-mbl" style={{ minHeight: '150px', width: '100%' }}>
            <div className="d-flex flex-row gap-4 justify-content-center" style={{ width: '100%' }}>
              <CTASection
                title="Start Your Financial Journey"
                cta="Sign Up"
                link="/signup#account-signup"
                theme='signup'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-blue-section"
                titleStyle={{ color: 'var(--text-medium-blue)' }}
              />
              <CTASection
                title="Request Info about this Fund"
                cta="Learn More"
                link="/learn?topic=smartbeta"
                theme='learn'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-blue-section"
                titleStyle={{ color: 'var(--text-medium-blue)' }}
              />
            </div>
          </div>
        </div>
      </section>





      {/* Our Investment Process Section */}
      <section className="full-width-dark-section" style={{
        backgroundColor: FUNDS_COLORS.DARK_BLUE,
        paddingTop: 'var(--section-padding-y)',
        paddingBottom: 'var(--section-padding-y)',
        paddingLeft: '0',
        paddingRight: '0',
        position: 'relative'
      }}>
        {/* Floating bubbles */}
        <div className="fx-bubbles" aria-hidden="true" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 0,
          pointerEvents: 'none'
        }}>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
        </div>

        <div className="container text-white" style={{ paddingLeft: 'var(--container-padding-x)', paddingRight: 'var(--container-padding-x)', position: 'relative', zIndex: 1 }}>
          <div className="row justify-content-center mb-5">
            <div className="col-lg-10 text-center">
              <h2 className="h2 mb-4">Our Investment Process</h2>
              <p className="body-text text-center mb-0" style={{ color: 'var(--text-white)' }}>We follow a disciplined, research-driven approach to portfolio construction</p>
            </div>
          </div>

          <div className="row g-4 justify-content-center">
            {/* First row - 3 cards */}
            <div className="col-md-6 col-lg-3">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body text-center">
                  <h5 className="card-title mb-3" style={{
                    color: 'var(--text-white)',
                    fontSize: 'var(--font-size-lg)',
                    fontWeight: '600'
                  }}>
                    1. Research
                  </h5>
                  <p className="card-text" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
                    Comprehensive market analysis and fundamental research on crypto assets
                  </p>
                </div>
              </div>
            </div>

            <div className="col-md-6 col-lg-3">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body text-center">
                  <h5 className="card-title mb-3" style={{
                    color: 'var(--text-white)',
                    fontSize: 'var(--font-size-lg)',
                    fontWeight: '600'
                  }}>
                    2. Selection
                  </h5>
                  <p className="card-text" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
                    Rigorous screening process to identify assets that meet our investment criteria
                  </p>
                </div>
              </div>
            </div>

            <div className="col-md-6 col-lg-3">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body text-center">
                  <h5 className="card-title mb-3" style={{
                    color: 'var(--text-white)',
                    fontSize: 'var(--font-size-lg)',
                    fontWeight: '600'
                  }}>
                    3. Construction
                  </h5>
                  <p className="card-text" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
                    Strategic portfolio building with optimal weightings and risk management
                  </p>
                </div>
              </div>
            </div>

            <div className="col-md-6 col-lg-3">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body text-center">
                  <h5 className="card-title mb-3" style={{
                    color: 'var(--text-white)',
                    fontSize: 'var(--font-size-lg)',
                    fontWeight: '600'
                  }}>
                    4. Monitoring
                  </h5>
                  <p className="card-text" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
                    Continuous oversight and periodic rebalancing to maintain optimal exposure
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>



      {/* Section 8 - Final CTA */}
      <section id="funds-cta" className="full-width-medium-section" style={{ backgroundColor: FUNDS_COLORS.MEDIUM_BLUE, paddingTop: '30px', paddingBottom: '30px', paddingLeft: '0', paddingRight: '0', position: 'relative' }}>
        {/* Floating bubbles for final CTA section */}
        <div className="fx-bubbles" aria-hidden="true" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 0,
          pointerEvents: 'none'
        }}>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
        </div>

        <div className="container" style={{ paddingLeft: 'var(--container-padding-x)', paddingRight: 'var(--container-padding-x)', position: 'relative', zIndex: 1 }}>
          <CTASection
            title="Take your first step towards your Financial Future"
            cta="Sign Up"
            link="/signup#account-signup"
            theme="signup"
            titleStyle={{ color: 'var(--text-medium-blue)' }}
          />
        </div>
      </section>
    </div>
    </>
  );
};

export default Funds;
