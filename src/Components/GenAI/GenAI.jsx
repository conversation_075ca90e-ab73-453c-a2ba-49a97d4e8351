import React from 'react';
import { Link } from 'react-router-dom';
import 'bootstrap/dist/js/bootstrap.bundle.min';
import '../../App.css';
import CTASection from '../../App/CTA/CTASection';
import FullHeightHero from '../Common/FullHeightHero';
import FundChart from '../Homepage/FundChart.jsx';
import { COLORS, SEMANTIC_COLORS } from '../../constants/colors';
import { applyPreset } from '../Common/StripeBackgroundConfig';

const GenAI = () => {
	// Fund data for GenAI chart - must be defined before use
	const costReward = [
		{
			name: "Cost Reduction",
			performance: 90, // Market data aggregation efficiency
			risk: 85, // Sentiment analysis accuracy
			fees: 75, // Transaction pattern detection
			liquidity: 80 // Strategy backtesting efficiency
		}
	];

	const risk_strategies = [
		{
			id: "Moolah-Approach",
			useVideo: true,
			videoSrc: "/funds-background.mp4",
			title: "Automated Research and Idea Generation",
			text: `<p>Generative AI streamlines our research pipeline by ingesting and processing large datasets, including price feeds, order book depth, blockchain transaction flows, sentiment metrics, and macroeconomic indicators. Using natural language processing (NLP) and large language models (LLMs), we scan news sources, whitepapers, governance proposals, and social channels to detect early signals of market-moving events.</p>
			<p>This automation shifts the workload from manual data gathering to higher-value activities like strategy calibration, risk scenario modelling, and portfolio optimisation.</p>
			<p>These AI-driven research capabilities are embedded into the AlphaGlobal GenAI LLM Funds for customised portfolio creation and also enhance tactical positioning in the AlphaGlobal Special Situations Fund.</p>`
		},
		{
			id: "Moolah-Efficiency",
			title: "Enhanced Portfolio Construction",
			image: "/AI image1.jpg",
			text: `<p>AI-driven portfolio optimisation models process multidimensional data sets — including price history, volatility surfaces, correlation matrices, liquidity profiles, and on-chain activity — to identify the most efficient asset mixes for a given risk target. Factor analysis and regime detection algorithms help adjust weightings based on prevailing market conditions, such as bullish momentum phases or liquidity contractions.</p>
			<p>These models dynamically adapt as market structures evolve, ensuring diversification remains effective under changing volatility regimes.</p>
			<p>This approach underpins the allocation process in the AlphaGlobal Market Index Fund, AlphaGlobal Momentum Fund, and AlphaGlobal DeFi Leaders Fund, while also supporting the targeted positioning of the AlphaGlobal Special Situations Fund.</p>`
			},
		{
			id: "Moolah-Speed",
			title: "Real-Time Market Response",
			image: "/strategies-quant.jpg",
			text: `<p>Large language models (LLMs) and real-time analytics pipelines process and interpret market-moving events — from breaking news and regulatory announcements to shifts in sentiment detected on social platforms and blockchain governance forums. Using natural language processing (NLP) and entity recognition, our systems classify events by relevance, market impact probability, and historical response patterns.</p>
			<p>By integrating automated alerting and scenario modelling, we can respond to both anticipated catalysts and unexpected shocks with speed and precision.</p>
			<p>These real-time adaptive capabilities are a cornerstone of the AlphaGlobal GenAI LLM Funds and play a critical role in the tactical overlays of the AlphaGlobal Special Situations Fund.</p>`
		},
		{
			id: "Moolah-Cost",
			title: "Cost Reduction",
			useChart: true,
			chartData: costReward[0],
			text: `<p>Generative AI automates labour-intensive tasks such as market data aggregation, sentiment analysis, transaction pattern detection, and strategy backtesting. By integrating APIs with exchanges, DeFi protocols, and data providers, we minimise operational overhead and shorten time-to-market for new fund strategies.</p>
			<p>These efficiencies lower execution costs, reduce human error, and allow resources to be redirected toward innovation and risk oversight — benefits that compound over time for investors.</p>
			<p>This cost-efficient infrastructure supports all AlphaGlobal strategies, from the Market Index Fund and Momentum Fund to the DeFi Leaders and Special Situations Funds, with the most advanced automation powering the GenAI LLM Funds.</p>`
		}
	];

	// Fund data for GenAI chart
	const fundData = [
		{
			name: "GenAI Fund",
			performance: 90,
			risk: 55,
			fees: 75,
			liquidity: 80
		}
	];



	return (
		<div className='text-black'>
			<section style={{
				backgroundColor: COLORS.DARK_BLUE,
				minHeight: '70vh',
				display: 'flex',
				alignItems: 'center',
				justifyContent: 'center',
				position: 'relative'
			}}>
				{/* Floating bubbles for hero section */}
				<div className="fx-bubbles" aria-hidden="true" style={{
					position: 'absolute',
					top: 0,
					left: 0,
					right: 0,
					bottom: 0,
					zIndex: 0,
					pointerEvents: 'none'
				}}>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
				</div>

				<div className="container text-center" style={{ position: 'relative', zIndex: 1 }}>
					<h1 className="display-4 text-white mb-4">GenAI and LLM Technology</h1>
					<p className="lead text-white mb-5">AI-powered investment solutions</p>
				</div>
			</section>

			{/* Section - We fuse index investing with GenAI */}
			<section className="full-width-medium-section" style={{
				backgroundColor: COLORS.MEDIUM_BLUE,
				paddingTop: 'var(--section-padding-y)',
				paddingBottom: 'var(--section-padding-y)',
				paddingLeft: '0',
				paddingRight: '0',
				position: 'relative'
			}}>
				{/* Floating bubbles */}
				<div className="fx-bubbles" aria-hidden="true" style={{
					position: 'absolute',
					top: 0,
					left: 0,
					right: 0,
					height: '100%',
					zIndex: 0,
					pointerEvents: 'none'
				}}>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
				</div>

				<div className={`container text-black`} style={{
					fontWeight: 'normal',
					paddingLeft: '20px',
					paddingRight: '20px'
				}}>
					<div className="row align-items-center">
						<div className="col-lg-5 text-center text-center-mbl order-lg-2 order-1 mb-4 mb-lg-0">
							{/* Custom performance chart */}
							<div style={{
								backgroundColor: 'transparent',
								maxWidth: '1200px',
								margin: '0 auto',
								border: 'none',
								borderRadius: '0.5rem',
								boxShadow: 'none',
								padding: '20px',
								cursor: 'pointer',
								transition: 'transform 0.2s ease'
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.transform = 'scale(1.02)';
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.transform = 'scale(1)';
							}}>
								<div style={{ position: 'relative', height: '600px', width: '100%' }}>
									{/* Legend - top middle, one line, smaller font */}
									<div style={{
										position: 'absolute',
										top: '120px',
										left: '50%',
										transform: 'translateX(-50%)',
										display: 'flex',
										gap: '15px',
										fontSize: '12px',
										zIndex: 10
									}}>
										<span style={{ color: 'var(--text-medium-blue)' }}>
											<span style={{ color: COLORS.RED }}>●</span> GenAI Funds
										</span>
										<span style={{ color: 'var(--text-medium-blue)' }}>
											<span style={{ color: COLORS.FRAME2_COLOR }}>●</span> Market Index
										</span>
									</div>

									<svg width="100%" height="100%" viewBox="0 0 1200 600" style={{ overflow: 'visible', marginTop: '20px' }}>
										{/* Grid lines */}
										<defs>
											<pattern id="grid2" width="120" height="80" patternUnits="userSpaceOnUse">
												<path d="M 120 0 L 0 0 0 80" fill="none" stroke="#e0e0e0" strokeWidth="0.5" opacity="0.2"/>
											</pattern>
										</defs>
										<rect width="100%" height="100%" fill="url(#grid2)" />

										{/* Y-axis labels - 0%, 30%, 70% - more transparent */}
										<text x="40" y="520" fill="var(--text-medium-blue)" fontSize="18" textAnchor="start" opacity="0.5">0%</text>
										<text x="40" y="350" fill="var(--text-medium-blue)" fontSize="18" textAnchor="start" opacity="0.5">30%</text>
										<text x="40" y="150" fill="var(--text-medium-blue)" fontSize="18" textAnchor="start" opacity="0.5">70%</text>

										{/* X-axis labels - 2022, 2023, 2024 - more transparent */}
										<text x="250" y="560" fill="var(--text-medium-blue)" fontSize="18" textAnchor="middle" opacity="0.5">2022</text>
										<text x="600" y="560" fill="var(--text-medium-blue)" fontSize="18" textAnchor="middle" opacity="0.5">2023</text>
										<text x="950" y="560" fill="var(--text-medium-blue)" fontSize="18" textAnchor="middle" opacity="0.5">2024</text>

										{/* Performance line 1 - RED (very transparent) */}
										<path
											d="M 250 480 L 600 320 L 950 180"
											fill="none"
											stroke={COLORS.RED}
											strokeWidth="6"
											strokeLinecap="round"
											opacity="0.3"
										/>

										{/* Performance line 2 - FRAME2_COLOR (very transparent) */}
										<path
											d="M 250 500 L 600 380 L 950 250"
											fill="none"
											stroke={COLORS.FRAME2_COLOR}
											strokeWidth="6"
											strokeLinecap="round"
											opacity="0.3"
										/>

										{/* Data points for line 1 */}
										<circle cx="250" cy="480" r="8" fill={COLORS.RED} opacity="0.6" />
										<circle cx="600" cy="320" r="8" fill={COLORS.RED} opacity="0.6" />
										<circle cx="950" cy="180" r="8" fill={COLORS.RED} opacity="0.6" />

										{/* Data points for line 2 */}
										<circle cx="250" cy="500" r="8" fill={COLORS.FRAME2_COLOR} opacity="0.6" />
										<circle cx="600" cy="380" r="8" fill={COLORS.FRAME2_COLOR} opacity="0.6" />
										<circle cx="950" cy="250" r="8" fill={COLORS.FRAME2_COLOR} opacity="0.6" />
									</svg>
								</div>
							</div>
						</div>
						<div className="col-lg-7 order-lg-1 order-2">
							<h1 className="h1 mb-0" style={{ color: COLORS.TEXT_BLACK }}>
								We integrate generative AI and large language models into our investment process to enhance research capabilities, improve portfolio construction, and enable more responsive market analysis.
							</h1>
							<div className="text-lg-start text-center">
								<Link role="button" className="btn sec-4-button ms-lg-4 mt-4" to="/funds">
									Explore
								</Link>
								<Link role="button" className="btn sec-4-button ms-lg-2 mt-4" to="/signup">
									Get Started
								</Link>
							</div>
						</div>
					</div>
				</div>
			</section>

			{/* CTA Section #1 */}
			<section id='genai-cta1' className="full-width-dark-section" style={{ backgroundColor: COLORS.DARK_BLUE, paddingTop: 'var(--section-padding-small)', paddingBottom: 'var(--section-padding-small)', paddingLeft: '0', paddingRight: '0', position: 'relative' }}>
				{/* Floating bubbles for CTA Section #1 */}
				<div className="fx-bubbles" aria-hidden="true" style={{
					position: 'absolute',
					top: 0,
					left: 0,
					right: 0,
					bottom: 0,
					zIndex: 0,
					pointerEvents: 'none'
				}}>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
				</div>

				<div className="container text-center-mbl text-white" style={{ paddingLeft: 'var(--container-padding-x-large)', paddingRight: 'var(--container-padding-x-large)', position: 'relative', zIndex: 1 }}>
					<CTASection
						title="Start Your Financial Journey"
						cta="Sign Up"
						link="/signup#account-signup"
						theme='signup'
						titleStyle={{ color: 'white' }}
					/>
				</div>
			</section>
			{/* Strategy Sections */}
			<div>
				{risk_strategies.map((strategy, index) => (
					<React.Fragment key={strategy.id}>
						<section className={
							index === 3 ? 'full-width-medium-section' :
							index % 2 === 0 ? 'full-width-medium-section' : 'full-width-dark-section'
						} style={{
							backgroundColor: index === 3 ? COLORS.MEDIUM_BLUE :
							index % 2 === 0 ? COLORS.MEDIUM_BLUE : COLORS.DARK_BLUE,
							minHeight: '500px',
							paddingTop: 'var(--section-padding-y)',
							paddingBottom: 'var(--section-padding-y)',
							display: 'flex',
							alignItems: 'center',
							color: index === 3 ? COLORS.TEXT_BLACK :
							index % 2 === 0 ? 'var(--text-medium-blue)' : 'var(--text-white)',
							position: 'relative'
						}}>
							{/* Add floating bubbles to all sections - both medium blue and dark navy */}
							<div className="fx-bubbles" aria-hidden="true" style={{
								position: 'absolute',
								top: 0,
								left: 0,
								right: 0,
								height: '100%',
								zIndex: 0,
								pointerEvents: 'none'
							}}>
								<span className="b"></span>
								<span className="b"></span>
								<span className="b"></span>
								<span className="b"></span>
								<span className="b"></span>
							</div>

							<div className={`container`} style={{
								color: index === 3 ? COLORS.TEXT_BLACK :
								index % 2 === 0 ? 'var(--text-medium-blue)' : 'var(--text-white)',
								paddingLeft: 'var(--container-padding-x-large)',
								paddingRight: 'var(--container-padding-x-large)',
								position: 'relative',
								zIndex: 1
							}}>
								<div className="row g-5">
									<div className={`col-lg-5 my-4 ${
										index === 3 ? 'order-lg-1' :
										index % 2 === 0 ? 'order-lg-1' : 'order-lg-2'
									}`}>
										<h2 className="h2 text-start mb-4 mobile-header" style={{
											color: index === 3 ? COLORS.TEXT_BLACK : 'inherit'
										}}>{strategy.title}</h2>
										<div className="body-text text-start" style={{
											color: index === 3 ? COLORS.TEXT_BLACK : 'inherit'
										}} dangerouslySetInnerHTML={{ __html: strategy.text }}></div>
									</div>
									<div className={`col-lg-7 my-4 d-flex justify-content-center align-items-center ${
										index === 3 ? 'order-lg-2' :
										index % 2 === 0 ? 'order-lg-2' : 'order-lg-1'
									}`}>
										<div className="fund-image-container" style={{ maxWidth: strategy.useVideo ? '800px' : strategy.useChart ? '400px' : '600px', width: '100%' }}>
											{strategy.useChart ? (
												<div style={{
													backgroundColor: 'transparent',
													maxWidth: '400px',
													width: '100%',
													margin: '0 auto',
													border: 'none',
													borderRadius: 'var(--border-radius-md)',
													boxShadow: 'none'
												}}>
													<FundChart
														fundData={strategy.chartData}
														compact={false}
														textColor={index === 3 ? 'text-dark' : 'text-white'}
														backgroundColor="transparent"
														customLabels={{
															performance: "Market Data Aggregation",
															risk: "Sentiment Analysis",
															fees: "Transaction Pattern Detection",
															liquidity: "Strategy Backtesting"
														}}
													/>
												</div>
											) : strategy.useVideo ? (
												<video
													src={strategy.videoSrc}
													autoPlay
													loop
													muted
													playsInline
													style={{ maxWidth: '100%', maxHeight: '600px', objectFit: 'cover', width: '100%', borderRadius: '12px' }}
												/>
											) : (
												<img
													src={strategy.image}
													alt={strategy.title}
													className='no-hover'
													style={{ maxWidth: '100%', maxHeight: '450px', objectFit: 'cover', width: '100%', borderRadius: '12px' }}
													onError={(e) => {
														e.target.onerror = null;
														e.target.src = "/placeholder.jpg";
													}}
												/>
											)}
										</div>
									</div>
								</div>
							</div>
						</section>

						{/* Insert GenAI Funds section after "Real-Time Market Response" (index 2) and before "Cost Reduction" (index 3) */}
						{index === 2 && (
							<section id="genai-funds" className='position-relative text-white overflow-hidden full-width-dark-section' style={{ backgroundColor: COLORS.DARK_BLUE, minHeight: '500px', paddingTop: '40px', paddingBottom: '40px', paddingLeft: '0', paddingRight: '0', display: 'flex', alignItems: 'center' }}>
								{/* Floating bubbles for GenAI Funds section */}
								<div className="fx-bubbles" aria-hidden="true" style={{
									position: 'absolute',
									top: 0,
									left: 0,
									right: 0,
									bottom: 0,
									zIndex: 0,
									pointerEvents: 'none'
								}}>
									<span className="b"></span>
									<span className="b"></span>
									<span className="b"></span>
									<span className="b"></span>
									<span className="b"></span>
								</div>

								<div className="container text-white" style={{ paddingLeft: 'var(--container-padding-x)', paddingRight: 'var(--container-padding-x)', position: 'relative', zIndex: 1 }}>
									<div className='row'>
										<div className='col-lg-6 my-3'>
											<h2 className='h2 text-start mb-4 mobile-header' style={{ marginTop: '0' }}>Moolah Capital GenAI Funds</h2>
											<p className='body-text text-start'>Our proprietary AI platform ingests vast amounts of blockchain, market, and sentiment data, using advanced algorithms and LLM reasoning to:</p>
											<ul className='body-text text-start'>
											 <li><span style={{ fontWeight: 'bold' }}>Identify </span> emerging opportunities across sectors, protocols, and tokens.</li>
											 <li><span style={{ fontWeight: 'bold' }}>Optimise</span> asset selection and weighting based on volatility, liquidity, and momentum factors.</li>
											 <li><span style={{ fontWeight: 'bold' }}>Generate</span>  bespoke strategies that align with investor-defined risk tolerance, time horizon, and thematic preferences.</li>
											 <li><span style={{ fontWeight: 'bold' }}>Design </span> their own AI-powered portfolios by setting goals and constraints.</li>
											 <li><span style={{ fontWeight: 'bold' }}>Replicate</span> top-performing AI strategies created by other investors.</li>
											</ul>
											<p className='body-text text-start'>By blending the personalisation of self-directed investing with the rigour of institutional-grade AI analytics, the <strong>Moolah Capital AlphaGlobal GenAI LLM Funds</strong> Funds give investors access to a flexible, constantly evolving investment toolkit — one that can capture opportunities beyond the reach of traditional portfolio management methods.</p>
										</div>
										<div className='col-lg-6 my-3 d-flex justify-content-center align-items-center' style={{ padding: 'var(--container-padding-x)' }}>
											<div style={{
												backgroundColor: 'transparent',
												maxWidth: '600px',
												width: '100%',
												margin: '0 auto',
												border: 'none',
												borderRadius: 'var(--border-radius-md)',
												boxShadow: 'none'
											}}>
												<FundChart
													fundData={fundData[0]}
													compact={false}
													textColor='text-white'
													backgroundColor="transparent"
												/>
											</div>
										</div>
									</div>
									{/* CTA Section - Full Width */}
									<div className="d-flex justify-content-center align-items-center my-4 text-center-mbl" style={{ minHeight: '150px', width: '100%' }}>
										<div className="d-flex flex-row gap-4 justify-content-center" style={{ width: '100%' }}>
											<CTASection
												title="Start Your Financial Journey"
												cta="Sign Up"
												link="/signup#account-signup"
												theme='signup'
												style={{ width: '45%', minWidth: '250px' }}
												className="cta-dark-section"
											/>
											<CTASection
												title="Request Info about this Fund"
												cta="Learn More"
												link="/learn?topic=smartbeta"
												theme='learn'
												style={{ width: '45%', minWidth: '250px' }}
												className="cta-dark-section"
											/>
										</div>
									</div>
								</div>
							</section>
						)}
					</React.Fragment>
				))}
			</div>

			{/* Examples of mini ETFs Section */}
			<section
				className="full-width-dark-section"
				id="mini-etfs-examples"
				style={{
					backgroundColor: COLORS.DARK_BLUE,
					paddingTop: '120px',
					paddingBottom: '120px',
					paddingLeft: '0',
					paddingRight: '0',
					position: 'relative'
				}}
			>
				{/* Floating bubbles */}
				<div className="fx-bubbles" aria-hidden="true" style={{
					position: 'absolute',
					top: 0,
					left: 0,
					right: 0,
					bottom: 0,
					zIndex: 0,
					pointerEvents: 'none'
				}}>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
				</div>

				<div className="container text-white" style={{ paddingLeft: 'var(--container-padding-x)', paddingRight: 'var(--container-padding-x)', position: 'relative', zIndex: 1 }}>
					<div className="row justify-content-center mb-5">
						<div className="col-lg-10 text-center">
							<h2 className="h2 mb-4">Examples of mini ETFs</h2>
						</div>
					</div>

					<div className="row g-4 justify-content-center">
						{/* First row - 4 cards */}
						<div className="col-md-6 col-lg-3">
							<div className="card h-100" style={{
								backgroundColor: 'var(--white-10)',
								border: 'none',
								borderRadius: 'var(--border-radius-md)',
								backdropFilter: 'blur(10px)'
							}}>
								<div className="card-body text-center">
									<h5 className="card-title mb-3" style={{
										color: 'var(--text-white)',
										fontSize: '1.2rem',
										fontWeight: '600'
									}}>
										🏆 DeFi Top 5
									</h5>
									<p className="card-text" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
										A basket of the top 5 DeFi tokens by market cap
									</p>
								</div>
							</div>
						</div>

						<div className="col-md-6 col-lg-3">
							<div className="card h-100" style={{
								backgroundColor: 'var(--white-10)',
								border: 'none',
								borderRadius: 'var(--border-radius-md)',
								backdropFilter: 'blur(10px)'
							}}>
								<div className="card-body text-center">
									<h5 className="card-title mb-3" style={{
										color: 'var(--text-white)',
										fontSize: '1.2rem',
										fontWeight: '600'
									}}>
										⚖️ Balanced Core
									</h5>
									<p className="card-text" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
										A portfolio split 50% Ethereum, 30% Bitcoin, 20% stablecoin yield products
									</p>
								</div>
							</div>
						</div>

						<div className="col-md-6 col-lg-3">
							<div className="card h-100" style={{
								backgroundColor: 'var(--white-10)',
								border: 'none',
								borderRadius: 'var(--border-radius-md)',
								backdropFilter: 'blur(10px)'
							}}>
								<div className="card-body text-center">
									<h5 className="card-title mb-3" style={{
										color: 'var(--text-white)',
										fontSize: '1.2rem',
										fontWeight: '600'
									}}>
										📦 Fund of Funds
									</h5>
									<p className="card-text" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
										A fund of funds combining three existing crypto funds into one vehicle
									</p>
								</div>
							</div>
						</div>

						<div className="col-md-6 col-lg-3">
							<div className="card h-100" style={{
								backgroundColor: 'var(--white-10)',
								border: 'none',
								borderRadius: 'var(--border-radius-md)',
								backdropFilter: 'blur(10px)'
							}}>
								<div className="card-body text-center">
									<h5 className="card-title mb-3" style={{
										color: 'var(--text-white)',
										fontSize: '1.2rem',
										fontWeight: '600'
									}}>
										🎯 Thematic Focus
									</h5>
									<p className="card-text" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
										A thematic allocation around gaming tokens or real-world asset protocols
									</p>
								</div>
							</div>
						</div>
					</div>

					<div className="row g-4 justify-content-center mt-4">
						{/* Second row - 4 cards */}
						<div className="col-md-6 col-lg-3">
							<div className="card h-100" style={{
								backgroundColor: 'var(--white-10)',
								border: 'none',
								borderRadius: 'var(--border-radius-md)',
								backdropFilter: 'blur(10px)'
							}}>
								<div className="card-body text-center">
									<h5 className="card-title mb-3" style={{
										color: 'var(--text-white)',
										fontSize: '1.2rem',
										fontWeight: '600'
									}}>
										🌱 Green Crypto
									</h5>
									<p className="card-text" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
										A Green Crypto Portfolio combining renewable energy tokens, carbon credit projects, and ESG-aligned assets
									</p>
								</div>
							</div>
						</div>

						<div className="col-md-6 col-lg-3">
							<div className="card h-100" style={{
								backgroundColor: 'var(--white-10)',
								border: 'none',
								borderRadius: 'var(--border-radius-md)',
								backdropFilter: 'blur(10px)'
							}}>
								<div className="card-body text-center">
									<h5 className="card-title mb-3" style={{
										color: 'var(--text-white)',
										fontSize: '1.2rem',
										fontWeight: '600'
									}}>
										💎 Stable Yield
									</h5>
									<p className="card-text" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
										A Stable Yield Portfolio mixing stablecoins, staking products, and yield-bearing DeFi protocols
									</p>
								</div>
							</div>
						</div>

						<div className="col-md-6 col-lg-3">
							<div className="card h-100" style={{
								backgroundColor: 'var(--white-10)',
								border: 'none',
								borderRadius: 'var(--border-radius-md)',
								backdropFilter: 'blur(10px)'
							}}>
								<div className="card-body text-center">
									<h5 className="card-title mb-3" style={{
										color: 'var(--text-white)',
										fontSize: '1.2rem',
										fontWeight: '600'
									}}>
										🚀 Next-Gen Tech
									</h5>
									<p className="card-text" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
										A Next-Gen Tech Portfolio with blockchain infrastructure tokens, layer-2 scaling projects, and AI-integrated protocols
									</p>
								</div>
							</div>
						</div>

						<div className="col-md-6 col-lg-3">
							<div className="card h-100" style={{
								backgroundColor: 'var(--white-10)',
								border: 'none',
								borderRadius: 'var(--border-radius-md)',
								backdropFilter: 'blur(10px)'
							}}>
								<div className="card-body text-center">
									<h5 className="card-title mb-3" style={{
										color: 'var(--text-white)',
										fontSize: '1.2rem',
										fontWeight: '600'
									}}>
										🎯 Diversified Blend
									</h5>
									<p className="card-text" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
										A Balanced Crypto Fund of Funds blending existing crypto funds and indexes into a single diversified product
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="full-width-medium-section" style={{ backgroundColor: COLORS.MEDIUM_BLUE, paddingTop: 'var(--section-padding-small)', paddingBottom: 'var(--section-padding-small)', paddingLeft: '0', paddingRight: '0', position: 'relative' }}>
				{/* Floating bubbles for final CTA section */}
				<div className="fx-bubbles" aria-hidden="true" style={{
					position: 'absolute',
					top: 0,
					left: 0,
					right: 0,
					bottom: 0,
					zIndex: 0,
					pointerEvents: 'none'
				}}>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
					<span className="b"></span>
				</div>

				<div className="container text-center-mbl" style={{ paddingLeft: 'var(--container-padding-x-large)', paddingRight: 'var(--container-padding-x-large)', position: 'relative', zIndex: 1 }}>
					<CTASection
						title="Explore AI-Powered Investment Strategies"
						cta="Learn More"
						link="/learn?topic=genai"
						theme='learn'
						titleStyle={{ color: 'var(--text-medium-blue)' }}
					/>
				</div>
			</section>
		</div>
	);
}

export default GenAI;
