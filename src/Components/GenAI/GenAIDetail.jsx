import { useParams } from 'react-router-dom';

const GenAIDetail = () => {
  const { genaiId } = useParams();

  const genaiData = [
    // Add GenAI specific components here when needed
  ];

  // Find the genai item that matches the genaiId
  const genai = genaiData.find(item => item.id === genaiId);

  // If no item matches, show a "not found" message
  if (!genai) {
    return <div className='text-black py-5 my-5 display-4 fw-bold'>GenAI &apos;{genaiId}&apos; not found</div>;
  }

  return (
    <>
    {genai.component}
    </>
  );
}

export default GenAIDetail;
