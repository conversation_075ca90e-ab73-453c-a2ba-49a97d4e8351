import React from 'react';


const CyberRisk = () => {

	return (

		<div className='text-black'>

			<div className='mb-5 py-5'>
				<div className='container py-5'>
					<h1 className='display-3 fw-bold body-text mb-5'>Legal Risk</h1>
					<div className='row'>
						<div className='col-lg-5 body-text'>
							<p>
								A crypto investment fund faces significant legal risks when buying, selling, and holding digital assets on behalf of investors. Unclear or evolving regulations across jurisdictions may impact fund operations, leading to compliance issues, regulatory penalties, or trading restrictions.
							<p />
								The classification of cryptocurrencies as securities, commodities, or unregulated assets varies, affecting how trades are executed and taxed. Custodial arrangements, counterparty risks, and anti-money laundering (AML) obligations also create legal exposure.
							<p />
								Additionally, smart contract failures or disputes over asset ownership can result in legal battles. Ensuring robust legal frameworks, compliance monitoring, and jurisdictional due diligence is critical to mitigating these risks.
							</p>
						</div>
						{/* Right Column: Images */}
						<div className="col-lg-6 text-center">
							<img
								alt="Legal Risk Measures"
								className="img-fluid rounded mb-4 shadow"
								src="https://www.todaysriskmanager.com/wp-content/uploads/2024/04/a-woman-reading-a-document-the-concept-of-legislation-on-cryptocurrencies-legal-regulation.jpg"
								onError={(e) => {
									e.target.onerror = null; // Prevent infinite loop
									e.target.src = "/placeholder.jpg";
								}}
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}

export default CyberRisk;
