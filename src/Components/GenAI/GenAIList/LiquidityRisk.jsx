import React from 'react';


const LiquidityRisk = () => {

	return (

		<div className='text-black'>

			<div className='mb-5 py-5'>
				<div className='container py-5'>
					<h1 className='display-3 fw-bold body-text mb-5'>Liquidity Risk</h1>
					<div className='row'>
						<div className='col-lg-5 body-text'>
							<p>
								Liquidity risk is a major concern for a crypto investment fund like Moolah when buying, selling, or holding digital assets in both centralized and decentralized markets.
							</p>
							<p>
								Market depth and order book imbalances on centralized exchanges (CEXs) can lead to slippage and execution delays, especially for large trades. In decentralized finance (DeFi) markets, liquidity can be fragmented across multiple pools, with high price impact and reliance on automated market makers (AMMs).
							</p>
							<p>
								Additionally, withdrawal limits, smart contract failures, or regulatory restrictions can lock up funds. Effective liquidity management, including diversified trading venues, risk monitoring, and dynamic execution strategies, is crucial to minimize these risks.
							</p>
						</div>
						{/* Right Column: Images */}
						<div className="col-lg-6 text-center">
							<img
								alt="Liquidity Risk Measures"
								className="img-fluid rounded mb-4 shadow"
								src="https://funderpro.com/wp-content/uploads/2023/08/Liquidity-in-Crypto-What-It-Is-and-Why-Its-Important--1024x576.jpg"
								onError={(e) => {
									e.target.onerror = null; // Prevent infinite loop
									e.target.src = "/placeholder.jpg";
								}}
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}

export default LiquidityRisk;
