import React from 'react';


const MarketRisk = () => {

	return (
		<div className='text-black'>
			<div className='mb-5 py-5'>
				<div className='container py-5'>
					<h1 className='display-3 fw-bold body-text mb-5'>Market Risk</h1>
					<div className='row'>
						<div className='col-lg-5 body-text'>
							<p>
								A crypto investment fund like Moolah Capital faces market risk when buying, selling, and holding digital assets across centralized exchanges (CEXs) and decentralized markets (DEXs). High volatility in crypto prices can lead to sudden losses, impacting portfolio value.
							</p>
							<p>
								Liquidity risks may arise, making it difficult to execute large trades without price slippage. Regulatory changes, exchange downtime, and smart contract failures in DeFi can further exacerbate risks.
							</p>
							<p>
								Additionally, market manipulation, flash crashes, and liquidation events in leveraged positions can amplify losses. Effective risk management, including hedging strategies, stop-loss mechanisms, and diversified exposure, is critical to mitigating these market uncertainties.
							</p>
						</div>
						{/* Right Column: Images */}
						<div className="col-lg-6 text-center">
							<img
								alt="Market Risk Measures"
								className="img-fluid rounded mb-4 shadow"
								src="https://cdn.corporatefinanceinstitute.com/assets/market-risk-1024x682.jpeg"
								onError={(e) => {
									e.target.onerror = null; // Prevent infinite loop
									e.target.src = "/placeholder.jpg";
								}}
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}

export default MarketRisk;
