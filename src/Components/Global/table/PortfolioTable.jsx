import React from 'react'

const PortfolioTable = ({ cols, data }) => {
    return (
        <div className='table-responsive my-5 body-text mx-auto body-text'>
            <table className="table mb-0">
                <thead>
                    <tr>
                        {
                            cols.map((col, index) => (
                                <th
                                    className={`border-0 ${index === 0 && 'ps-5'}`}
                                    key={index}
                                    scope="col" >
                                        <span className='h3'>{col}</span>
                                </th>
                            ))
                        }
                    </tr>
                </thead>
                <tbody>
                    {
                        data.map((data, index) => (
                            <tr key={index}>
                                <td className="py-3 ps-5 fw-bold">{data.name}</td>
                                <td className='py-3'>{data.coin}</td>
                                <td className='py-3'>{data.price}</td>
                                <td className='py-3'>{data.volume.toLocaleString()}</td>
                                <td className='py-3'>{data.change}</td>
                                <td className='py-3'>{data.market_cap.toLocaleString()}</td>
                            </tr>
                        ))
                    }
                </tbody>
            </table>
        </div>
    )
}

export default PortfolioTable