import React from 'react';
import { COLORS, SEMANTIC_COLORS } from '../../constants/colors';

const FundChart = ({
  fundData,
  compact = false,
  textColor = "text-white",
  backgroundColor = "transparent",
  customLabels = null
}) => {
  const getColorStyle = (color) => {
    const colorMap = {
      'bg-green-500': SEMANTIC_COLORS.CHART_PERFORMANCE_BAR,
      'bg-red-500': SEMANTIC_COLORS.CHART_RISK_BAR,
      'bg-blue-500': SEMANTIC_COLORS.CHART_FEES_BAR,
      'bg-purple-500': SEMANTIC_COLORS.CHART_LIQUIDITY_BAR
    };
    return colorMap[color] || SEMANTIC_COLORS.CHART_FEES_BAR;
  };

  const ProgressBar = ({ label, value, color = "bg-blue-500" }) => (
    <div className={compact ? "mb-2" : "mb-3"}>
      <div className="d-flex justify-content-between mb-1">
        <span className={`${compact ? 'small' : 'small'} fw-medium ${textColor}`}>{label}</span>
        <span className={`${compact ? 'small' : 'small'} ${textColor === 'text-white' ? 'text-white-50' : 'text-muted'}`}>{value}%</span>
      </div>
      <div className="progress" style={{ height: compact ? '6px' : '8px' }}>
        <div 
          className="progress-bar"
          role="progressbar"
          style={{ 
            width: `${value}%`,
            backgroundColor: getColorStyle(color),
            transition: 'width 0.5s ease'
          }}
          aria-valuenow={value}
          aria-valuemin="0"
          aria-valuemax="100"
        ></div>
      </div>
    </div>
  );

  if (!fundData) return null;

  const finalBackgroundColor = backgroundColor;

  // Default labels or custom labels
  const labels = customLabels || {
    performance: "Performance",
    risk: "Risk Level",
    fees: "Low Fees",
    liquidity: "Liquidity"
  };

  return (
    <div
      className={`${compact ? 'p-3' : 'p-4'} h-100`}
      style={{
        backgroundColor: finalBackgroundColor
      }}
    >
      <h4 className={`${compact ? 'h6' : 'h5'} fw-bold mb-3 ${textColor}`}>{fundData.name}</h4>

      <ProgressBar
        label={labels.performance}
        value={fundData.performance}
        color="bg-green-500"
      />

      <ProgressBar
        label={labels.risk}
        value={fundData.risk}
        color="bg-red-500"
      />

      <ProgressBar
        label={labels.fees}
        value={fundData.fees}
        color="bg-blue-500"
      />

      <ProgressBar
        label={labels.liquidity}
        value={fundData.liquidity}
        color="bg-purple-500"
      />
      
      <div className={`${compact ? 'mt-3 pt-2' : 'mt-4 pt-3'} border-top ${textColor === 'text-white' ? 'border-white-50' : 'border-secondary'}`}>
        <div className={`small ${textColor === 'text-white' ? 'text-white-50' : 'text-muted'}`}>
          Overall Score:
          <span className={`fw-bold ms-1 ${textColor}`}>
            {Math.round((fundData.performance + fundData.fees + fundData.liquidity - fundData.risk) / 3)}%
          </span>
        </div>
      </div>
    </div>
  );
};

export default FundChart;
