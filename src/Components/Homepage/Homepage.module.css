.panelCard {
  min-height: 500px;
}

.sectionWhite {
  background-color: var(--white);
}

.sectionGray {
  background-color: var(--medium-blue);
}

.panelDescription {
  color: var(--text-white);
  text-align: left;
  margin-bottom: 1rem;
  padding-top: 1rem;
}

.panelTitle {
  color: var(--text-white);
}

/* Video container styles */
.videoContainer {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background-color: transparent;
  z-index: 0;
}

.videoPlaceholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: cover;
  background-position: center;
}

.backgroundVideo {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
