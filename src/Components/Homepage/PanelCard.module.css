.panelCard {
  border-radius: 16px;
  padding: 24px;
  background-color: var(--card-color);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  color: var(--text-white);
  box-shadow: 0 4px 16px var(--black-20);
  transition: transform 0.3s ease;
}

.panelCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 20px var(--black-30);
}

.panelExploreButton {
  background-color: var(--button-red) !important;
  color: var(--button-text) !important;
  border: none !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  padding: 8px 24px !important;
  border-radius: 50px !important;
  font-weight: 500;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
  font-family: var(--font-body);
  text-align: center;
  display: inline-block;
  width: auto;
  margin: 0 auto;
  box-shadow: 0 4px 12px var(--button-shadow) !important;
}

.panelExploreButton:hover {
  background-color: var(--button-red) !important;
  color: var(--button-text) !important;
  transform: scale(1.05);
  box-shadow: 0 6px 18px var(--button-shadow) !important;
}

.panelDescription {
  color: var(--text-white);
  text-align: left;
  margin-bottom: 1rem;
  padding-top: 1rem;
}

.panelDescription p {
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
}

.panelDescription p:last-child {
  margin-bottom: 0;
}

.panelDescription ul, .panelDescription ol {
  padding-left: 1.5rem;
  margin-bottom: 0.75rem;
}

.panelDescription li {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .panelExploreButton {
    display: block;
    width: 80%;
    margin: 0 auto;
    text-align: center;
  }

  .panelCard .mt-4 {
    text-align: center;
    display: flex;
    justify-content: center;
  }
}

