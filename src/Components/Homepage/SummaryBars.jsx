import React from 'react';
import { COLORS, SEMANTIC_COLORS } from '../../constants/colors';

const SummaryBars = () => {
  const fundData = [
    {
      name: "AlphaGlobal Market Index Fund",
      performance: 85,
      risk: 30,
      fees: 95, // Higher = lower fees
      liquidity: 90
    },
    {
      name: "AlphaGlobal Momentum Fund", 
      performance: 78,
      risk: 65,
      fees: 70,
      liquidity: 85
    },
    {
      name: "AlphaGlobal DeFi Leaders Fund",
      performance: 82,
      risk: 45,
      fees: 80,
      liquidity: 88
    },
    {
      name: "AlphaGlobal Yield Fund",
      performance: 87,
      risk: 47,
      fees: 75,
      liquidity: 88
    }
  ];

  const getColorStyle = (color) => {
    const colorMap = {
      'bg-green-500': SEMANTIC_COLORS.CHART_PERFORMANCE_BAR,
      'bg-red-500': SEMANTIC_COLORS.CHART_RISK_BAR,
      'bg-blue-500': SEMANTIC_COLORS.CHART_FEES_BAR,
      'bg-purple-500': SEMANTIC_COLORS.CHART_LIQUIDITY_BAR
    };
    return colorMap[color] || SEMANTIC_COLORS.CHART_FEES_BAR;
  };

  const ProgressBar = ({ label, value, color = "bg-blue-500" }) => (
    <div className="mb-3">
      <div className="d-flex justify-content-between mb-1">
        <span className="small fw-medium text-dark">{label}</span>
        <span className="small text-muted">{value}%</span>
      </div>
      <div className="progress" style={{ height: '8px' }}>
        <div
          className="progress-bar"
          role="progressbar"
          style={{
            width: `${value}%`,
            backgroundColor: getColorStyle(color),
            transition: 'width 0.5s ease'
          }}
          aria-valuenow={value}
          aria-valuemin="0"
          aria-valuemax="100"
        ></div>
      </div>
    </div>
  );

  return (
    <div className="container-fluid" style={{ maxWidth: '1200px' }}>
      {/* <h2 className="h2 text-center mb-5 text-dark">Fund Performance Summary</h2> */}



      {/* Alternative: Single comparison chart */}
      {/* <div className="mt-5 p-4 rounded" style={{ backgroundColor: '#f8f9fa' }}>
        <h3 className="h4 fw-bold mb-4 text-dark">Performance Comparison</h3>
        <div className="d-flex flex-column gap-3">
          {fundData.map((fund, index) => (
            <div key={index} className="d-flex align-items-center">
              <div className="text-start fw-medium text-dark me-3" style={{ minWidth: '140px', fontSize: '0.9rem' }}>
                {fund.name}
              </div>
              <div className="flex-fill me-2">
                <div className="progress" style={{ height: '16px' }}>
                  <div
                    className="progress-bar"
                    role="progressbar"
                    style={{
                      width: `${fund.performance}%`,
                      background: 'linear-gradient(90deg, #60a5fa, #3b82f6)',
                      transition: 'width 0.5s ease'
                    }}
                    aria-valuenow={fund.performance}
                    aria-valuemin="0"
                    aria-valuemax="100"
                  ></div>
                </div>
              </div>
              <span className="small fw-bold text-dark" style={{ minWidth: '40px' }}>
                {fund.performance}%
              </span>
            </div>
          ))}
        </div>
      </div> */}
    </div>
  );
};

export default SummaryBars;
