import React, { useState, useMemo, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import emailjs from '@emailjs/browser';

const Learn = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [form, setForm] = useState({
        subject: 'moolah:capital:learn',
        email: '',
        name: '',
        fundType: '',
        otherFundType: '',
        message: ''
    });
    const [statusMessage, setStatusMessage] = useState('');
    const [statusType, setStatusType] = useState('');
    const [loading, setLoading] = useState(false);
    const [showForm, setShowForm] = useState(true);

    // Parse query parameters on component mount
    useEffect(() => {
        const params = new URLSearchParams(location.search);
        const topic = params.get('topic');
        
        if (topic) {
            // Direct mapping from URL parameter to form state
            let fundType;
            switch(topic.toLowerCase()) {
                case 'passive':
                    fundType = 'passive';
                    break;
                case 'smartbeta':
                    fundType = 'smartBeta';
                    break;
                case 'special':
                    fundType = 'specialSituations';
                    break;
                case 'risk':
                    fundType = 'risk';
                    break;
                case 'other':
                    fundType = 'other';
                    break;
                default:
                    fundType = '';
            }
            
            if (fundType) {
                setForm(prevForm => ({
                    ...prevForm,
                    fundType
                }));
            }
        }
    }, [location.search]);

    // Handle input change
    const handleChange = (e) => {
        const { name, value } = e.target;
        setForm({ ...form, [name]: value });
    };

    // Handle radio button change
    const handleRadioChange = (e) => {
        const { value } = e.target;
        
        // Directly set the fundType without any conditional logic
        setForm(prevForm => ({ 
            ...prevForm, 
            fundType: value,
            // Clear otherFundType if a predefined option is selected
            otherFundType: value === 'other' ? prevForm.otherFundType : ''
        }));
        
        // Update URL with the selected fund type
        const fundTypeToParam = {
            'passive': 'passive',
            'smartBeta': 'smartbeta',
            'specialSituations': 'special',
            'other': 'other',
            'risk': 'risk'
        };
        
        const newParam = fundTypeToParam[value];
        if (newParam) {
            // Update URL without reloading the page
            navigate(`/learn?topic=${newParam}`, { replace: true });
        } else {
            // If no valid param, remove the query parameter
            navigate('/learn', { replace: true });
        }
    };

    const statusMessageElement = useMemo(() => {
        if (typeof statusMessage === 'string' && statusMessage.trim() !== '') {
            return (
                <div className={`mt-4 ms-8 text-center ${statusType === 'success' ? 'text-success' : 'text-danger'}`}>
                    {statusMessage}
                </div>
            );
        }
        return null;
    }, [statusMessage, statusType]);

    // Scroll to top function
    const scrollToTop = () => {
        window.scrollTo(0, 0);
    };

    // Handle form submission
    const sendEmail = async (e) => {
        e.preventDefault();
        if (loading) return;
        
        // Validate form
        if (!form.fundType) {
            setStatusMessage('Please select a fund type.');
            setStatusType('error');
            return;
        }
        
        if (form.fundType === 'other' && !form.otherFundType.trim()) {
            setStatusMessage('Please specify the other fund type.');
            setStatusType('error');
            return;
        }
        
        setLoading(true);
        setStatusMessage('');
        setStatusType('');

        try {
            // Prepare the message with fund type information
            const fundTypeMessage = form.fundType === 'other' 
                ? `Fund Type: Other - ${form.otherFundType}` 
                : `Fund Type: ${form.fundType}`;
                
            const fullMessage = `${fundTypeMessage}\n\n${form.message}`;
            
            await emailjs.send(
                'service_sm28k8v',
                'template_3z6s8zt',
                {
                    ...form,
                    subject: 'moolah:capital:learn - ' + form.subject,
                    message: fullMessage,
                    to_email: '<EMAIL>'
                },
                {
                    publicKey: '8sgwUMQOfLINQUnrQ',
                }
            );
            setStatusMessage('Your request for info has been submitted. Thank you!');
            setStatusType('success');
            setForm({
                ...form,
                email: '',
                name: '',
                fundType: '',
                otherFundType: ''
            });
            setShowForm(false);
        } catch (err) {
            setStatusMessage('There was an error sending your form. Please try again later.');
            setStatusType('error');
            console.error('Email send error:', err);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="d-flex flex-column justify-content-center align-items-center text-black py-5 my-5" style={{ paddingTop: '120px' }}>
            <div className="container text-center">
                
                <div className="d-flex flex-column align-items-center justify-content-center gap-3">
                    {!showForm ? (
                        <div className="mt-4 text-center">
                            <h3 className="h3 mb-3">Thank You!</h3>
                            <p className="body-text mb-4">
                                We've received your message and will be in touch soon with more information.
                            </p>
                            <Link 
                                role="button"
                                to="/funds"
                                className="btn sec-4-button px-5 py-3 rounded-pill" 
                                onClick={scrollToTop}>
                                Explore Our Funds
                            </Link>
                        </div>
                    ) : (
                        <div className="mt-5 form-pop contact-box rounded-lg p-4" style={{ maxWidth: '600px' }}>
                            <h2 className="h3 mb-4">Tell Us About Your Investment Interests</h2>
                            <form onSubmit={sendEmail}>
                                {/* Hidden fields */}
                                <input type="hidden" name="subject" value={form.subject} />
                                
                                {/* Name field */}
                                <div className="mb-3">
                                    <label htmlFor="name" className="form-label small text-black fw-bold">
                                        Your Name
                                    </label>
                                    <input
                                        type="text"
                                        className="form-control"
                                        id="name"
                                        name="name"
                                        value={form.name}
                                        onChange={handleChange}
                                        required
                                    />
                                </div>
                                
                                {/* Email field */}
                                <div className="mb-3">
                                    <label htmlFor="email" className="form-label small text-black fw-bold">
                                        Your Email
                                    </label>
                                    <input
                                        type="email"
                                        className="form-control"
                                        id="email"
                                        name="email"
                                        value={form.email}
                                        onChange={handleChange}
                                        required
                                    />
                                </div>
                                
                                {/* Fund Type Radio Buttons */}
                                <div className="mb-4">
                                    <label className="form-label small text-black fw-bold d-block mb-2">
                                        Which fund type or topic interests you?
                                    </label>
                                    
                                    <div className="form-check text-start mb-2">
                                        <input
                                            className="form-check-input"
                                            type="radio"
                                            name="fundType"
                                            id="passiveFund"
                                            value="passive"
                                            checked={form.fundType === 'passive'}
                                            onChange={handleRadioChange}
                                        />
                                        <label className="form-check-label" htmlFor="passiveFund">
                                            Passive Funds
                                        </label>
                                    </div>
                                    
                                    <div className="form-check text-start mb-2">
                                        <input
                                            className="form-check-input"
                                            type="radio"
                                            name="fundType"
                                            id="smartBetaFund"
                                            value="smartBeta"
                                            checked={form.fundType === 'smartBeta'}
                                            onChange={handleRadioChange}
                                        />
                                        <label className="form-check-label" htmlFor="smartBetaFund">
                                            Smart Beta Funds
                                        </label>
                                    </div>
                                    
                                    <div className="form-check text-start mb-2">
                                        <input
                                            className="form-check-input"
                                            type="radio"
                                            name="fundType"
                                            id="specialSituationsFund"
                                            value="specialSituations"
                                            checked={form.fundType === 'specialSituations'}
                                            onChange={handleRadioChange}
                                        />
                                        <label className="form-check-label" htmlFor="specialSituationsFund">
                                            Special Situations Funds
                                        </label>
                                    </div>
                                    
                                    <div className="form-check text-start mb-2">
                                        <input
                                            className="form-check-input"
                                            type="radio"
                                            name="fundType"
                                            id="otherFund"
                                            value="other"
                                            checked={form.fundType === 'other'}
                                            onChange={handleRadioChange}
                                        />
                                        <label className="form-check-label" htmlFor="otherFund">
                                            Other
                                        </label>
                                    </div>

                                    <div className="form-check text-start mb-2">
                                        <input
                                            className="form-check-input"
                                            type="radio"
                                            name="fundType"
                                            id="riskFund"
                                            value="risk"
                                            checked={form.fundType === 'risk'}
                                            onChange={handleRadioChange}
                                        />
                                        <label className="form-check-label" htmlFor="riskFund">
                                            Risk
                                        </label>
                                    </div>
                                    
                                    {/* Other Fund Type Text Field - Only shown when "Other" is selected */}
                                    {form.fundType === 'other' && (
                                        <div className="mt-2 mb-3">
                                            <label htmlFor="otherFundType" className="form-label small text-black fw-bold">
                                                Please specify
                                            </label>
                                            <input
                                                type="text"
                                                className="form-control"
                                                id="otherFundType"
                                                name="otherFundType"
                                                value={form.otherFundType}
                                                onChange={handleChange}
                                                placeholder="add your area of interest here[optional]"
                                            />
                                        </div>
                                    )}
                                </div>
                                
                                {/* Additional Comments */}
                                <div className="mb-4">
                                    <label htmlFor="message" className="form-label small text-black fw-bold">
                                        Additional Comments (Optional)
                                    </label>
                                    <textarea
                                        className="form-control"
                                        id="message"
                                        name="message"
                                        rows="3"
                                        value={form.message}
                                        onChange={handleChange}
                                    ></textarea>
                                </div>
                                
                                {/* Buttons */}
                                <div className="d-flex justify-content-between mt-4">
                                    <button
                                        type="submit"
                                        className="btn smooth-center sec-4-button px-4 py-2"
                                        disabled={loading}
                                    >
                                        {loading ? (
                                            <>
                                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                                Sending...
                                            </>
                                        ) : (
                                            "Submit"
                                        )}
                                    </button>
                                </div>
                                {statusMessageElement}
                            </form>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Learn;
