import React, { useState, useMemo } from 'react';
import { Link, useLocation } from 'react-router-dom';
import emailjs from '@emailjs/browser';

const NotFound = () => {
    const location = useLocation();
    const [form, setForm] = useState({
        subject: '404 Error Report',
        email: '',
        name: '',
        message: `Page not found: ${location.pathname}\nDate & Time: ${new Date().toLocaleString()}`
    });
    const [statusMessage, setStatusMessage] = useState('');
    const [statusType, setStatusType] = useState('');
    const [loading, setLoading] = useState(false);
    const [showForm, setShowForm] = useState(false);

    // Handle input change
    const handleChange = (e) => {
        setForm({ ...form, [e.target.name]: e.target.value });
    };

    const statusMessageElement = useMemo(() => {
        if (typeof statusMessage === 'string' && statusMessage.trim() !== '') {
            return (
                <div className={`mt-4 ms-8 text-center ${statusType === 'success' ? 'text-success' : 'text-danger'}`}>
                    {statusMessage}
                </div>
            );
        }
        return null;
    }, [statusMessage, statusType]);

    // Scroll to top function
    const scrollToTop = () => {
        window.scrollTo(0, 0);
    };

    // Handle form submission
    const sendEmail = async (e) => {
        e.preventDefault();
        if (loading) return;
        
        setLoading(true);
        setStatusMessage('');
        setStatusType('');

        try {
            form.subject = 'moolah:capital:404 - '+form.subject;
            await emailjs.send(
                'service_sm28k8v',
                'template_3z6s8zt',
                {
                    ...form,
                    to_email: '<EMAIL>'
                },
                {
                    publicKey: '8sgwUMQOfLINQUnrQ',
                }
            );
            setStatusMessage('Error report sent successfully. Thank you for helping us improve!');
            setStatusType('success');
            setForm({
                ...form,
                email: '',
                name: ''
            });
            setShowForm(false);
        } catch (err) {
            setStatusMessage('There was an error sending your report. Please try again later.');
            setStatusType('error');
            console.error('Email send error:', err);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="d-flex flex-column justify-content-center align-items-center text-black py-5 my-5" style={{ paddingTop: '120px' }}>
            <div className="container text-center">
                <h1 className="display-1 fw-bold">404</h1>
                <h2 className="h2 mb-4">Page Not Found</h2>
                <p className="body-text smooth-center mb-5">Sorry, looks like you tried to use a page that doesn&apos;t exist.</p>
                
                <div className="d-flex flex-column align-items-center justify-content-center gap-3">
                    <Link 
                        role="button"
                        to="/"
                        className="btn sec-4-button px-5 py-3 rounded-pill" 
                        onClick={scrollToTop}>
                        Return to Homepage
                    </Link>
                    
                    {!showForm ? (
                        <button 
                            className="btn btn-outline-secondary px-5 py-3 rounded-pill"
                            onClick={() => setShowForm(true)}
                        >
                            Report This Error
                        </button>
                    ) : (
                        <div className="mt-5 form-pop contact-box rounded-lg p-4" style={{ maxWidth: '600px' }}>
                            <h3 className="h3 mb-3">Report Error Details</h3>
                            <p className="body-text mb-4">
                                Help us improve by reporting this error. We&apos;ll receive the page you tried to access and when it happened.
                            </p>
                            
                            <form onSubmit={sendEmail}>
                                {/* Hidden fields */}
                                <input type="hidden" name="subject" value={form.subject} />
                                <input type="hidden" name="message" value={form.message} />
                                
                                {/* Name field */}
                                <div className="mb-3">
                                    <label htmlFor="name" className="form-label small text-black fw-bold">
                                        Your Name (optional)
                                    </label>
                                    <input
                                        type="text"
                                        className="form-control"
                                        id="name"
                                        name="name"
                                        value={form.name}
                                        onChange={handleChange}
                                    />
                                </div>
                                
                                {/* Email field */}
                                <div className="mb-3">
                                    <label htmlFor="email" className="form-label small text-black fw-bold">
                                        Your Email (optional)
                                    </label>
                                    <input
                                        type="email"
                                        className="form-control"
                                        id="email"
                                        name="email"
                                        value={form.email}
                                        onChange={handleChange}
                                    />
                                </div>
                                
                                {/* Error details (read-only) */}
                                <div className="mb-3">
                                    <label htmlFor="errorDetails" className="form-label small text-black fw-bold">
                                        Error Details
                                    </label>
                                    <div 
                                        id="errorDetails"
                                        className="form-control bg-light" 
                                        style={{ minHeight: '80px' }}
                                        role="textbox"
                                        aria-readonly="true"
                                        aria-label="Error details"
                                    >
                                        <p className="mb-1">Page not found: {location.pathname}</p>
                                        <p className="mb-0">Date & Time: {new Date().toLocaleString()}</p>
                                    </div>
                                </div>
                                
                                {/* Buttons */}
                                <div className="d-flex justify-content-between mt-4">
                                    <button
                                        type="button"
                                        className="btn btn-outline-secondary px-4 py-2"
                                        onClick={() => setShowForm(false)}
                                        disabled={loading}
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        type="submit"
                                        className="btn sec-4-button px-4 py-2"
                                        disabled={loading}
                                    >
                                        {loading ? (
                                            <>
                                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                                Sending...
                                            </>
                                        ) : (
                                            "Send Report"
                                        )}
                                    </button>
                                </div>
                                {statusMessageElement}
                            </form>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default NotFound;
