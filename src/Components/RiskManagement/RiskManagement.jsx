import React, { useState } from 'react';
import 'bootstrap/dist/js/bootstrap.bundle.min';
import CTASection from '../../App/CTA/CTASection';
import FullHeightHero from '../Common/FullHeightHero';

const RiskManagement = () => {
	const [activeIndex, setActiveIndex] = useState(null);

	const risks = [
		{
			question: 'Market Risk',
			definition: 'Exposure to losses from market prices fluctuations. Crypto assets are highly volatile, leading to significant price swings.',
			management: 'Use Value-at-Risk (VaR) models estimate potential losses over a set period. Strict control of leverage is critical to minimize losses.',
			mitigation: 'Use of hedging strategies (options, futures), portfolio diversification, and setting stop-loss limits.'
		},
		{
			question: 'Liquidity Risk',
			definition: ' Inability to meet obligations for payments and redemptions due to less than perfectly liquid assets.',
			management: 'Measure and use liquidity metrics such as Liquidity Coverage Ratio(LCR) used in traditional finance.',
			mitigation: 'Balance liquid reserves with illiquid investments. Align term structure assets and liabilities. Structure redemption terms and apply fees for requests to redeem funds at short notice.'
		},
		{
			question: 'Credit Risk',
			definition: 'Exposure to third parties such as exchanges, custodians, or counterparties in derivatives trading.',
			management: 'Managed exposures with strict alignment to fund objectives and liquidity requirements via ALM. Verify and risk test counterparties. Measure and manage concentration risks by coin/fund/regulatory regime.',
			mitigation: 'Conducting thorough due diligence, diversifying counterparties, and using smart contracts with decentralized finance (DeFi) protocols cautiously.'
		},
		{
			question: 'Operational Risk',
			definition: 'Inefficiencies in trading execution, order processing, and compliance failures e.g., internal failures like systems, fraud, cyberattacks.',
			management: ' Robust IT infrastructure. Continuous education and training of staff. Automated compliance tools to ensure practice follows policy. Applied & verified cybersecurity protocols.',
			mitigation: 'Automating processes where possible, implementing robust risk management frameworks, and conducting regular stress tests.'
		},
		{
			question: 'Legal Risk',
			definition: ' Penalties from non-compliance e.g. MICA regulations(Europe), AML/KYC (multiple locations). Regulatory Uncertainty – Evolving regulations across different jurisdictions can impact fund operations.',
			management: ' On-call legal teams, applied regulatory technology (RegTech) and jurisdictional diversification.',
			mitigation: 'Legal and compliance teams tracking legal updates, structuring funds in crypto-friendly jurisdictions, and maintaining flexible investment strategies.'
		},
		{
			question: 'Regulatory Risk',
			definition: 'Regulatory risk is the threat of adverse changes in laws or regulations impacting a crypto fund’s operations, legality, or profitability.',
			management: 'Moolah capital can manage regulatory risk by monitoring legal developments, ensuring compliance, engaging legal counsel, and diversifying across jurisdictions.',
			mitigation: 'Crypto funds can’t fully mitigate regulatory risk, but proactive compliance, legal counsel, and jurisdictional diversification help manage potential impacts.'
		}
	];

	const risk_strategies = [
		{
			id: "Moolah-Approach",
			image: "/risk-riskmanagement.jpg",
			title: "Moolah Approach to Risk Management",
			text: "At Moolah, risk management is central to our investment philosophy and culture. We apply a proactive, multi-layered approach across asset classes and styles, enabling our affiliate managers to spot emerging risks early and act decisively to protect and grow capital. As markets evolve, so do risk sources—so our strategies are built to adapt. Institutions seeking a forward-thinking risk partner, and private investors aiming to preserve and grow wealth, are invited to connect with our advisory team for tailored insights."
		},
		{
			id: "Moolah-Balance",
			title: "Balancing Risk and Reward Across Market Cycles",
			image: "/risk-riskreward.jpg",
			text: "Our goal is to balance risk and opportunity in any market. We combine macro insights with asset-level analysis to monitor key risk factors like market volatility, credit quality, liquidity, and operational resilience. Our institutional strategies aim to manage volatility while driving long-term results—contact us to learn more. For private clients, we offer tailored, risk-aligned solutions that balance growth with protection."
		},
		{
			id: "Moolah-Diversified",
			title: "A Diversified, Global Perspective",
			image: "/risk-diverseglobal.jpg",
			text: `<div class="diversified-perspective-container">
      <p class="body-text text-start">With a global footprint and multi-manager model, Moolah provides institutional investors with access to a broad range of strategies designed to:</p>
      <ul class="diversified-benefits-list">
        <li class="diversified-benefit-item">Mitigate losses in market downturns</li>
        <li class="diversified-benefit-item">Capture opportunities in volatile conditions</li>
        <li class="diversified-benefit-item">Diversify through public and private alternatives</li>
        <li class="diversified-benefit-item">Deliver dynamic multi-asset solutions</li>
      </ul>
      <p class="body-text text-start">Explore how your institution can benefit from our global perspective and proven multi-manager approach.</p>
      <p class="body-text text-start">Private investors and family offices also gain access to strategies traditionally reserved for institutions.</p>
    </div>`
		},
		{
			id: "Moolah-4Step",
			title: "Our Four-Step Risk Framework",
			image: "/risk-4step.jpg",
			text: `<div class="risk-framework-container">
      <div class="risk-step-item">
        <strong>1. Identify:</strong> <span>We systematically map potential risks across market, credit, liquidity, operational, and regulatory dimensions.</span>
      </div>
      <div class="risk-step-item">
        <strong>2. Measure:</strong> <span>Using quantitative models and stress tests, we assess each risk's potential impact and likelihood.</span>
      </div>
      <div class="risk-step-item">
        <strong>3. Mitigate:</strong> <span>We implement targeted strategies to reduce exposure through diversification, hedging, and operational controls.</span>
      </div>
      <div class="risk-step-item">
        <strong>4. Monitor:</strong> <span>Continuous oversight ensures our risk profile remains aligned with investment objectives as markets evolve.</span>
      </div>
    </div>`
		}
	];

	// Toggle functions for each section
	const toggleAccordion = index => {
		setActiveIndex(activeIndex === index ? null : index);
	};

	const renderAccordion = (risks, activeIndex, toggleAccordion) => (
		<div className="accordion" id="accordionFlushExample">
			{risks.map((risk, index) => (
				<div className="accordion-item" key={index}>
					<div className="accordion-header ">
						<button
							className={`accordion-button ${activeIndex === index ? '' : 'collapsed'}`}
							data-bs-toggle="tooltip"
							onClick={() => toggleAccordion(index)}
							title={`click to view details about ${risk.question}`}
							type="button" >
							<span className='h2c'>{risk.question}</span>
						</button>
					</div>
					<div
						className={`accordion-collapse collapse ${activeIndex === index ? 'show' : ''}`}>
						<div className='accordion-body'>
							<div className='d-flex risk-faq-container my-2 mt-4'>
								<p className='h4 risk-label'>Definition: </p>
								<p className="body-text-small text-left risk-content">{risk.definition}</p>
							</div>
							<div className='d-flex risk-faq-container mb-2'>
								<p className='h4 risk-label'>Management: </p>
								<p className="body-text-small text-left risk-content">{risk.management}</p>
							</div>
							<div className='d-flex risk-faq-container '>
								<p className='h4 risk-label'>Mitigation: </p>
								<p className="body-text-small text-left risk-content">{risk.mitigation}</p>
							</div>
						</div>
					</div>
				</div>
			))}
		</div>
	);

	return (
		<div className='text-black'>
			<FullHeightHero
				backgroundVideo="/iStock-2160279068.mp4"
				posterImage="/loading-bullbear.jpg"
				overlayColor="rgba(0, 0, 0, 0.5)"
				videoPlaybackRate={0.3}
			>
				<div className="container text-center">
					<h1 className="display-4 text-white mb-4">Risk Management</h1>
					<p className="lead text-white mb-5">Strong Risk Plans For Smart Decisions</p>
				</div>
			</FullHeightHero>

			<div className="page-content-container" id="risk-content">
				{/* Section 2 = Risk Management Overview */}
			{risk_strategies.map((strategy, index) => (
				<section
					key={index}
					id={`risk-strategy-${strategy.id}`}
					className='body-content-py-margin position-relative text-black overflow-hidden body-content-px-margin'
					style={{ backgroundColor: index % 2 === 0 ? '#f9f9f9' : '#ffffff' }}
				>
					<div className='container'>
						<div className={`row align-items-center ${index % 2 !== 0 ? 'flex-row-reverse' : ''}`}>
							<div className="col-lg-7 my-3">
								<div className="">
									<div className="h2c fw-bold text-start mb-4">
										{strategy.title}
										{strategy.icon && (
											<span className="ms-2">{strategy.icon}</span>
										)}
									</div>
									{typeof strategy.text === 'string' ? (
										<div
											className="body-text ms-4 text-start"
											dangerouslySetInnerHTML={{ __html: strategy.text }}
										/>
									) : (
										<div className="body-text ms-4 text-center-mbl text-start">
											{strategy.text}
										</div>
									)}
								</div>
							</div>
							<div className="col-lg-5 text-center text-center-mbl">
								{strategy.image && (
									<img
										alt={strategy.title}
										className="img-fluid w-75 rounded-lg shadow"
										src={strategy.image || "/img-16.jpg"}
										onError={(e) => {
											e.target.onerror = null; // Prevent infinite loop
											e.target.src = "/placeholder.jpg";
										}}
									/>
								)}
								{!strategy.image && (
									<img
										alt="Risk Management"
										className="img-fluid w-75 rounded-lg shadow"
										src="/img-16.jpg"
										onError={(e) => {
											e.target.onerror = null; // Prevent infinite loop
											e.target.src = "/placeholder.jpg";
										}}
									/>
								)}
							</div>
						</div>
					</div>
				</section>
			))}

			{/* Section 3 - Risk Management List */}
			<section className="body-content-py-margin" style={{ backgroundColor: '#ffffff' }}>
				<div className="container body-content-px-margin">
					<div className="row">
						<div className="col-12">
							<div className="h2 fw-bold mb-4" style={{ top: '100px', textAlign: 'left' }} >
								Managing Specific Risks
							</div>
						</div>
					</div>
					<div className="row">
						<div className="col-12">
							{renderAccordion(risks, activeIndex, toggleAccordion)}
						</div>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section style={{ backgroundColor: '#ffffff' }}>
				<div className="text-center-mbl">
				<CTASection
					title="We turn Risk into Opportunity"
					cta="Learn more about Risk"
					link="/learn?topic=risk"
					theme='learn'
				/>
				</div>
			</section>
		</div>
		</div>
	);
}
export default RiskManagement;
