import React from 'react';


const CreditRisk = () => {

	return (
		<div className='text-black'>
			<div className='mb-5 py-5'>
				<div className='container py-5'>
					<h1 className='display-3 fw-bold body-text mb-5'>Credit Risk</h1>
					<div className='row'>
						<div className='col-lg-5 body-text'>
							<p>
								A crypto investment fund faces credit risk when relying on third parties such as crypto exchanges, custodians, DeFi platforms, and lending protocols.
							</p>
							<p>
								If an exchange becomes insolvent or suffers a security breach, assets may become inaccessible or lost. Custodians holding investor funds could face liquidity issues or operational failures, putting assets at risk.
							</p>
							<p>
								DeFi markets expose funds to smart contract vulnerabilities, liquidity crises, or counterparty defaults in lending and borrowing.
							</p>
							<p>
								To mitigate credit risk, funds must diversify counterparties, conduct due diligence, use insured custodians, and monitor real-time credit exposure to safeguard investor capital in volatile crypto markets.
							</p>
						</div>
						<div className="col-lg-6 text-center">
							<img
								alt="Credit Risk Measures"
								className="img-fluid rounded mb-4 shadow"
								src="https://blog.bankerscompliance.com/hubfs/crypto%20risk.jpeg#keepProtocol"
								onError={(e) => {
									e.target.onerror = null; // Prevent infinite loop
									e.target.src = "/placeholder.jpg";
								}}
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}

export default CreditRisk;
