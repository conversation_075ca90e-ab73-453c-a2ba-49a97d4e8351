import React from 'react';


const CyberRisk = () => {

	return (
		<div className="text-black">
			<div className="mb-5 py-5">
				<div className="container py-5">
					<div className="h2 fw-bold body-text mb-5">Cyber Risk</div>
					<div className="row align-items-center">
						{/* Left Column: Text Content */}
						<div className="col-lg-6 body-text">
							<p>
								A crypto investment fund faces significant cyber risks when buying, selling, and holding digital assets on behalf of investors. These risks include exchange hacks, wallet security breaches, phishing attacks, insider threats, and smart contract vulnerabilities.
							</p>
							<p>
								Custodial wallets and centralized exchanges are prime targets for cybercriminals, leading to potential loss of funds. Poor key management, compromised private keys, or API leaks can expose assets to unauthorized access.
							</p>
							<p>
								Ensuring robust security measures, such as multi-signature wallets, cold storage, real-time monitoring, and strict access controls, is critical to protecting investor funds and minimizing the impact of cyber threats in volatile crypto markets.
							</p>
						</div>

						{/* Right Column: Images */}
						<div className="col-lg-6 text-center">
							<img
								alt="Cybersecurity Protection"
								className="img-fluid rounded mb-4 shadow"
								src="https://cypro.com.au/wp-content/uploads/RISK-MANAGEMENT-1.jpeg"
								onError={(e) => {
									e.target.onerror = null; // Prevent infinite loop
									e.target.src = "/placeholder.jpg";
								}}
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
export default CyberRisk;
