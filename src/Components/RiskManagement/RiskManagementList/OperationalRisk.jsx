import React from 'react';


const OperationalRisk = () => {

	return (

		<div className='text-black'>

			<div className='mb-5 py-5'>
				<div className='container py-5'>
					<h1 className='display-3 fw-bold body-text mb-5'>Operational Risk</h1>
					<div className='row'>
						<div className='col-lg-5 body-text'>
							<p>
								Crypto investment funds like Moolah Capital faces operational risks when buying, selling, and holding digital assets in both centralized (CEX) and decentralized (DEX) markets. Risks include trade execution failures, system outages, custody mismanagement, regulatory compliance issues, and smart contract vulnerabilities.
							</p>
							<p>
								Centralized exchanges may suffer from withdrawal restrictions or security breaches, while decentralized platforms expose funds to liquidity risks, protocol failures, and hacking threats. Poor private key management can lead to irreversible asset loss.
							</p>
							<p>
								To mitigate these risks, funds must implement robust security controls, automated monitoring, operational redundancies, and compliance frameworks to safeguard investor assets in volatile crypto markets.
							</p>
						</div>
						{/* Right Column: Images */}
						<div className="col-lg-6 text-center">
							<img
								alt="Operational Risk Measures"
								className="img-fluid rounded mb-4 shadow"
								src="/operational-risk-management.png"
								onError={(e) => {
									e.target.onerror = null; // Prevent infinite loop
									e.target.src = "/placeholder.jpg";
								}}
							/>
						</div>
						<div className='col-lg-1' />
					</div>
				</div>
			</div>

		</div>
	);
}
export default OperationalRisk;
