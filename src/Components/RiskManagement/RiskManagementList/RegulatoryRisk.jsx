import React from 'react';

const RegulatoryRisk = () => {
	return (
		<div className='text-black'>
			<div className='mb-5 py-5'>
				<div className='container py-5'>
					<h1 className='display-3 fw-bold body-text mb-5'>Regulatory Risk</h1>
					<div className='row'>
						<div className='col-lg-5 body-text'>
							<p>
								Regulatory risk for a fund trading in crypto assets refers to the potential for changes in laws, regulations, or government policies that could adversely impact the fund’s operations or profitability.
							</p>
							<p>
								This includes sudden bans, new licensing requirements, or restrictions on trading, custody, or asset classification. The evolving and often uncertain global regulatory landscape creates challenges in compliance and increases the risk of fines or forced closure. Funds must stay proactive and adaptive to mitigate these risks and protect investor capital.
							</p>
						</div>
						<div className="col-lg-6 text-center">
							<img
								alt="Regulatory Risk Measures"
								className="img-fluid rounded mb-4 shadow"
								src="https://blog.bankerscompliance.com/hubfs/crypto%20risk.jpeg#keepProtocol"
								onError={(e) => {
									e.target.onerror = null; // Prevent infinite loop
									e.target.src = "/placeholder.jpg";
								}}
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}

export default RegulatoryRisk;
