import React, { useState, useMemo, useRef, useEffect } from 'react';
import emailjs from '@emailjs/browser';
import ReCAPTCHA from 'react-google-recaptcha';
import { useLocation } from 'react-router-dom';

const AccountSignup = () => {
    // Form state
    const [form, setForm] = useState({ 
        subject: 'New Account', 
        name: '', 
        address: '', 
        email: '', 
        investorType: 'individual', 
        message: '' 
    });
    const [statusMessage, setStatusMessage] = useState('');
    const [statusType, setStatusType] = useState('');
    const [loading, setLoading] = useState(false);
    const [captchaVerified, setCaptchaVerified] = useState(false);
    const recaptchaRef = useRef(null);
    const formRef = useRef(null);
    const location = useLocation();

    // Handle hash navigation on page load with smoother scrolling
    useEffect(() => {
        // Check if there's a hash in the URL
        if (location.hash) {
            // Prevent default scroll behavior
            window.scrollTo(0, 0);
            
            // Wait for component to fully render
            setTimeout(() => {
                // Smooth scroll to form with proper offset
                if (formRef.current) {
                    const navbarHeight = 50; // Reduced from 100px to 50px
                    const rect = formRef.current.getBoundingClientRect();
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const targetPosition = scrollTop + rect.top - navbarHeight;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            }, 100);
        }
    }, [location]);

    // Handle input change
    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        if (type === 'radio') {
            if (checked) {
                setForm({ ...form, [name]: value });
            }
        } else {
            setForm({ ...form, [name]: value });
        }
    };

    const statusMessageElement = useMemo(() => {
        if (typeof statusMessage === 'string' && statusMessage.trim() !== '') {
            return (
                <div className={`mt-4 text-center ${statusType === 'success' ? 'text-success' : 'text-danger'}`}>
                    {statusMessage}
                </div>
            );
        }
        return null;
    }, [statusMessage, statusType]);

    // Handle reCAPTCHA verification
    const handleCaptchaChange = (token) => {
        setCaptchaVerified(!!token);
    };

    // Handle form submission
    const sendEmail = async (e) => {
        e.preventDefault();
        if (loading) return;
        
        // Verify reCAPTCHA
        if (!captchaVerified) {
            setStatusMessage('Please complete the CAPTCHA verification.');
            setStatusType('error');
            return;
        }

        setLoading(true);
        setStatusMessage('');
        setStatusType('');

        try {
            form.subject = 'moolah:capital:signup - '+form.subject;
            await emailjs.send(
                'service_sm28k8v',
                'template_3z6s8zt',
                form,
                {
                    publicKey: '8sgwUMQOfLINQUnrQ',
                }
            );
            setStatusMessage('Your account request has been submitted successfully! We will contact you shortly.');
            setStatusType('success');
            setForm({ 
                subject: 'New Moolah Capital Account', 
                name: '', 
                address: '', 
                email: '', 
                investorType: 'individual', 
                message: '' 
            });
            setCaptchaVerified(false);
            if (recaptchaRef.current) {
                recaptchaRef.current.reset();
            }
        } catch (err) {
            setStatusMessage('There was an error submitting your request. Please try again later.');
            setStatusType('error');
            console.error('Email send error:', err);
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <div id="account-signup" ref={formRef} className="d-flex justify-content-center align-items-center contact-bg text-black mb-5 pb-5 px-4" style={{ paddingTop: '80px' }}>
                <div className="contact_class">
                    <div className='mb-4 pb-2'>
                        <div className="h2c smooth-center font-bold">Sign Up for a Moolah Capital Account</div>
                        <div className="body-text">Complete the form below to create your Moolah Capital account.</div>
                    </div>
                    <p></p>
                    <form className="d-flex justify-content-center align-items-center" onSubmit={sendEmail}>
                        <div
                            className="form-pop contact-box rounded-lg p-lg-5 p-md-4 p-sm-3 p-3"
                            style={{ width: '100%', maxWidth: '700px', minWidth: '50%', }} >
                            {/* Honeypot spam protection */}
                            <input
                                type="text"
                                name="honeypot"
                                style={{ display: 'none' }}
                                tabIndex="-1"
                                autoComplete="off"
                            />
                            {/* Hidden subject field */}
                            <input
                                type="hidden"
                                name="subject"
                                value={form.subject}
                            />
                            {/* 'Name' field */}
                            <div className='body-text mb-3'>
                                <label htmlFor="name" className="form-label small text-black fw-bold">
                                    Full Name <span className="text-danger">*</span>
                                </label>
                                <input
                                    type="text"
                                    className="form-control w-100"
                                    id="name"
                                    name="name"
                                    value={form.name}
                                    onChange={handleChange}
                                    required />
                            </div>
                            {/* 'Address' field */}
                            <div className='body-text mb-3'>
                                <label htmlFor="address" className="form-label small text-black fw-bold">
                                    Address <span className="text-danger">*</span>
                                </label>
                                <textarea
                                    className="form-control"
                                    id="address"
                                    name="address"
                                    value={form.address}
                                    onChange={handleChange}
                                    required
                                    rows={2}
                                    placeholder="Enter your full address" />
                            </div>
                            {/* 'Email' field */}
                            <div className='body-text mb-3'>
                                <label htmlFor="email" className="form-label small text-black fw-bold">
                                    Email <span className="text-danger">*</span>
                                </label>
                                <input
                                    type="email"
                                    className="form-control"
                                    id="email"
                                    name="email"
                                    value={form.email}
                                    onChange={handleChange}
                                    required />
                            </div>
                            {/* 'Investor Type' field */}
                            <div className='body-text mb-3'>
                                <fieldset>
                                    <legend className="form-label small text-black fw-bold">
                                        Investor Type <span className="text-danger">*</span>
                                    </legend>
                                    <div className="d-flex gap-4 mt-2">
                                        <div className="form-check">
                                            <input
                                                className="form-check-input"
                                                type="radio"
                                                id="individualType"
                                                name="investorType"
                                                value="individual"
                                                checked={form.investorType === 'individual'}
                                                onChange={handleChange}
                                                required
                                            />
                                            <label className="form-check-label" htmlFor="individualType">
                                                Individual
                                            </label>
                                        </div>
                                        <div className="form-check">
                                            <input
                                                className="form-check-input"
                                                type="radio"
                                                id="institutionType"
                                                name="investorType"
                                                value="institution"
                                                checked={form.investorType === 'institution'}
                                                onChange={handleChange}
                                            />
                                            <label className="form-check-label" htmlFor="institutionType">
                                                Institution
                                            </label>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                            {/* 'Message' field */}
                            <div className='body-text mb-3'>
                                <label htmlFor="message" className="form-label small text-black fw-bold">
                                    Additional Information (optional)
                                </label>
                                <textarea
                                    className="form-control"
                                    id="message"
                                    name="message"
                                    value={form.message}
                                    onChange={handleChange}
                                    rows={3}
                                    placeholder="Please provide any additional information about your investment goals or questions" />
                            </div>
                            {/* 'Add to Email list' checkbox */}
                            <div>
                                <div className="form-check">
                                    <input
                                        className="form-check-input"
                                        type="checkbox"
                                        id="addToEmailList"
                                        name="addToEmailList"
                                        defaultChecked
                                    />
                                    <label className="form-check-label" htmlFor="addToEmailList">
                                        Add me to Moolah Capital email list
                                    </label>
                                </div>
                            </div>
                            {/* reCAPTCHA */}
                            <div className='my-4 d-flex justify-content-center'>
                                <ReCAPTCHA
                                    ref={recaptchaRef}
                                    sitekey="6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI" // Replace with your actual site key
                                    onChange={handleCaptchaChange}
                                />
                            </div>
                            {/* 'Submit' button */}
                            <div className='d-flex justify-content-center'>
                                <div className='text-start mt-4'>
                                    <button
                                        className='btn sec-4-button d-inline-flex align-items-center gap-2 px-5 py-3 rounded-pill'
                                        type="submit"
                                        disabled={loading || !captchaVerified}
                                    >
                                        {loading ? (
                                            <>
                                                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                <span>Processing...</span>
                                            </>
                                        ) : (
                                            "Sign up for Moolah Capital account"
                                        )}
                                    </button>
                                </div>
                            </div>
                            {statusMessageElement}
                        </div>
                    </form>
                </div>
            </div>
        </>
    );
};
export default AccountSignup;
