import React, { useState, useMemo, useEffect, useRef } from 'react';
import emailjs from '@emailjs/browser';
import { useLocation } from 'react-router-dom';

const EmailSignup = () => {
    // Form state
    const [form, setForm] = useState({ subject: 'Add to Moolah email list', email: '', message: '' });
    const [statusMessage, setStatusMessage] = useState('');
    const [statusType, setStatusType] = useState('');
    const [loading, setLoading] = useState(false);
    const location = useLocation();
    const formRef = useRef(null);

    // Handle hash navigation on page load with smoother scrolling
    useEffect(() => {
        // Check if there's a hash in the URL
        if (location.hash) {
            // Prevent default scroll behavior
            window.scrollTo(0, 0);
            
            // Wait for component to fully render
            setTimeout(() => {
                // Smooth scroll to form with proper offset
                if (formRef.current) {
                    const navbarHeight = 50; // Reduced from 100px to 50px to match AccountSignup
                    const rect = formRef.current.getBoundingClientRect();
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const targetPosition = scrollTop + rect.top - navbarHeight;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            }, 100);
        }
    }, [location]);

    // Handle input change
    const handleChange = (e) => {
        setForm({ ...form, [e.target.name]: e.target.value });
    };

    const statusMessageElement = useMemo(() => {
        if (typeof statusMessage === 'string' && statusMessage.trim() !== '') {
            return (
                <div className={`mt-4 text-center ${statusType === 'success' ? 'text-success' : 'text-danger'}`}>
                    {statusMessage}
                </div>
            );
        }
        return null;
    }, [statusMessage, statusType]);

    // Handle form submission
    const sendEmail = async (e) => {
        e.preventDefault();
        if (loading)
            return;
        setLoading(true);
        setStatusMessage('');
        setStatusType('');

        try {
            form.subject = 'moolah:capital:addemail - '+form.subject;
            await emailjs.send(
                'service_sm28k8v',
                'template_3z6s8zt',
                form,
                {
                    publicKey: '8sgwUMQOfLINQUnrQ',
                }
            );
            setStatusMessage('You have been successfully added to our email list!');
            setStatusType('success');
            setForm({ subject: 'Add to Moolah email list', email: '', message: '' });
        } catch (err) {
            setStatusMessage('There was an error adding you to our email list. Please try again later.');
            setStatusType('error');
            console.error('Email send error:', err);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="email-signup-container" ref={formRef}>
            <div 
                className="d-flex justify-content-center align-items-center contact-bg text-black mb-5 pb-5 px-4"
                style={{ paddingTop: '80px' }} // Changed from 120px to 80px to match AccountSignup
            >
                <div className="contact_class mt-5">
                    <div className='mb-4 pb-2'>
                        <div className="h2c smooth-center font-bold">Join Moolah Capital Email List</div>
                        <div className="body-text"> Stay updated with our latest news, investment insights, and opportunities. </div>
                    </div>
                    <p></p>
                    <form className="d-flex justify-content-center align-items-center" onSubmit={sendEmail}>
                        <div
                            className="form-pop contact-box rounded-lg p-lg-5 p-md-4 p-sm-3 p-3"
                            style={{ width: '100%', maxWidth: '700px', minWidth: '50%', }} >
                            {/* Honeypot spam protection */}
                            <input
                                type="text"
                                name="honeypot"
                                style={{ display: 'none' }}
                                tabIndex="-1"
                                autoComplete="off"
                            />
                            
                            {/* Hidden subject field with default value */}
                            <input
                                type="hidden"
                                name="subject"
                                value={form.subject}
                            />
                            
                            {/* 'Email' field */}
                            <div className='body-text mb-3'>
                                <label htmlFor="email" className="form-label small text-black fw-bold">
                                    Email <span className="text-danger">*</span>
                                </label>
                                <input
                                    type="email"
                                    className="form-control"
                                    id="email"
                                    name="email"
                                    value={form.email}
                                    onChange={handleChange}
                                    required />
                            </div>
                            
                            {/* 'Message' field (optional) */}
                            <div className='body-text mb-3'>
                                <label htmlFor="message" className="form-label small text-black fw-bold">
                                    Message (optional)
                                </label>
                                <textarea
                                    className="form-control"
                                    id="message"
                                    name="message"
                                    value={form.message}
                                    onChange={handleChange}
                                    rows={3}
                                    placeholder="Tell us what kind of investment information you're interested in" />
                            </div>
                            
                            {/* 'Submit' button */}
                            <div className='d-flex justify-content-center'>
                                <div className='text-start mt-4'>
                                    <button
                                        className='btn sec-4-button d-inline-flex align-items-center gap-2 px-5 py-3 rounded-pill'
                                        type="submit"
                                        disabled={loading}
                                    >
                                        {loading ? (
                                            <>
                                                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                <span>Processing...</span>
                                            </>
                                        ) : (
                                            "Add me to the Moolah Capital list"
                                        )}
                                    </button>
                                </div>
                            </div>
                            {statusMessageElement}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};
export default EmailSignup;
