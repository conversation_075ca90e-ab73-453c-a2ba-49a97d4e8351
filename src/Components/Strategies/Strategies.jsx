import React, { useState } from 'react';
import CTASection from '../../App/CTA/CTASection.jsx';
import FullHeightHero from '../Common/FullHeightHero';

// Import global colors and Stripe effects
import { COLORS } from '../../constants/colors';
import { applyPreset } from '../Common/StripeBackgroundConfig';

// Use global colors directly
const STRATEGIES_COLORS = COLORS;

const Strategies = () => {
  const [activeIndex, setActiveIndex] = useState(null);

  const risks = [
    {
      question: 'Market Risk',
      definition: 'Exposure to losses from market prices fluctuations. Crypto assets are highly volatile, leading to significant price swings.',
      management: 'Use Value-at-Risk (VaR) models estimate potential losses over a set period. Strict control of leverage is critical to minimize losses.',
      mitigation: 'Use of hedging strategies (options, futures), portfolio diversification, and setting stop-loss limits.'
    },
    {
      question: 'Liquidity Risk',
      definition: 'Inability to meet obligations for payments and redemptions due to less than perfectly liquid assets.',
      management: 'Measure and use liquidity metrics such as Liquidity Coverage Ratio(LCR) used in traditional finance.',
      mitigation: 'Balance liquid reserves with illiquid investments. Align term structure assets and liabilities. Structure redemption terms and apply fees for requests to redeem funds at short notice.'
    },
    {
      question: 'Credit Risk',
      definition: 'Exposure to third parties such as exchanges, custodians, or counterparties in derivatives trading.',
      management: 'Managed exposures with strict alignment to fund objectives and liquidity requirements via ALM. Verify and risk test counterparties. Measure and manage concentration risks by coin/fund/regulatory regime.',
      mitigation: 'Conducting thorough due diligence, diversifying counterparties, and using smart contracts with decentralized finance (DeFi) protocols cautiously.'
    },
    {
      question: 'Operational Risk',
      definition: 'Inefficiencies in trading execution, order processing, and compliance failures e.g., internal failures like systems, fraud, cyberattacks.',
      management: 'Robust IT infrastructure. Continuous education and training of staff. Automated compliance tools to ensure practice follows policy. Applied & verified cybersecurity protocols.',
      mitigation: 'Automating processes where possible, implementing robust risk management frameworks, and conducting regular stress tests.'
    },
    {
      question: 'Legal Risk',
      definition: 'Penalties from non-compliance e.g. MICA regulations(Europe), AML/KYC (multiple locations). Regulatory Uncertainty – Evolving regulations across different jurisdictions can impact fund operations.',
      management: 'On-call legal teams, applied regulatory technology (RegTech) and jurisdictional diversification.',
      mitigation: 'Legal and compliance teams tracking legal updates, structuring funds in crypto-friendly jurisdictions, and maintaining flexible investment strategies.'
    },
    {
      question: 'Regulatory Risk',
      definition: 'Regulatory risk is the threat of adverse changes in laws or regulations impacting a crypto fund\'s operations, legality, or profitability.',
      management: 'Moolah capital can manage regulatory risk by monitoring legal developments, ensuring compliance, engaging legal counsel, and diversifying across jurisdictions.',
      mitigation: 'Crypto funds can\'t fully mitigate regulatory risk, but proactive compliance, legal counsel, and jurisdictional diversification help manage potential impacts.'
    }
  ];

  const toggleAccordion = index => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  const renderAccordion = (risks, activeIndex, toggleAccordion, sectionBackground) => {
    return (
      <div className="accordion" id="accordionFlushExample" style={{ gap: '8px', display: 'flex', flexDirection: 'column' }}>
        {risks.map((risk, index) => {
          // Use contrasting blue background for accordion
          const accordionBackground = sectionBackground === STRATEGIES_COLORS.DARK_BLUE ? STRATEGIES_COLORS.MEDIUM_BLUE : STRATEGIES_COLORS.DARK_BLUE;
          // Set text color based on accordion background
          const textColor = accordionBackground === STRATEGIES_COLORS.MEDIUM_BLUE ? 'var(--text-medium-blue)' : 'var(--text-white)';
          // Set shadow color based on accordion background
          const shadowColor = accordionBackground === STRATEGIES_COLORS.MEDIUM_BLUE ? 'var(--black-10)' : 'var(--white-10)';

          return (
            <div className="accordion-item" key={index} style={{
              backgroundColor: accordionBackground,
              border: 'none',
              borderRadius: '12px',
              boxShadow: `0 2px 8px ${shadowColor}`,
              marginBottom: '0px',
              overflow: 'hidden',
              transition: 'transform 0.2s ease, box-shadow 0.2s ease'
            }}>
              <h2 className="accordion-header" id={`flush-heading${index}`}>
                <button
                  className={`accordion-button ${activeIndex === index ? '' : 'collapsed'}`}
                  type="button"
                  onClick={() => toggleAccordion(index)}
                  aria-expanded={activeIndex === index}
                  aria-controls={`flush-collapse${index}`}
                  style={{
                    backgroundColor: accordionBackground,
                    color: textColor,
                    border: 'none',
                    boxShadow: 'none',
                    borderRadius: activeIndex === index ? '12px 12px 0 0' : '12px',
                    padding: '16px 20px',
                    fontWeight: '500',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.target.closest('.accordion-item').style.transform = 'translateY(-2px)';
                    e.target.closest('.accordion-item').style.boxShadow = `0 4px 12px ${shadowColor}`;
                  }}
                  onMouseLeave={(e) => {
                    e.target.closest('.accordion-item').style.transform = 'translateY(0)';
                    e.target.closest('.accordion-item').style.boxShadow = `0 2px 8px ${shadowColor}`;
                  }}
                >
                  {risk.question}
                </button>
              </h2>
              <div
                id={`flush-collapse${index}`}
                className={`accordion-collapse collapse ${activeIndex === index ? 'show' : ''}`}
                aria-labelledby={`flush-heading${index}`}
              >
                <div className="accordion-body" style={{
                  backgroundColor: accordionBackground,
                  color: textColor,
                  padding: '16px 20px 20px 20px',
                  borderRadius: '0 0 12px 12px'
                }}>
                  <p><strong>Definition:</strong> {risk.definition}</p>
                  <p><strong>Management:</strong> {risk.management}</p>
                  <p><strong>Mitigation:</strong> {risk.mitigation}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const cardStrategyList = [
    { id: 1, title: 'Risk Management', helpText: 'AI-Powered Strategies', useVideo: true, videoSrc: '/iStock-2160279068.mp4', description: [
        '<p>We apply multiple approaches to create value for your crypto investments:</p>',
        '<ul><li>Smart Beta — systematic factor-based exposures tilting toward growth, value, or volatility.</li><li>Algorithmic — rule-driven models capturing inefficiencies and patterns in digital assets.</li><li>Professional Investment Managers — discretionary allocation and timing informed by market context.</li><li>GenAI — large language models generating investment ideas, asset mixes, and rebalancing strategies.</li></ul>',
        '<p>Together these strategies broaden the opportunity set and strengthen the foundations of long-term value creation.</p>'
      ]
    },
    { id: 2, title: 'DeFi Integration', helpText: 'Decentralised Finance', img: '/strategies-ai.jpg', description: [
        '<p>We leverage decentralized finance protocols to access new yield opportunities, liquidity pools, and innovative financial instruments.</p>',
        '<p>Our DeFi strategies include yield farming, liquidity provision, and participation in governance tokens, all while maintaining strict risk management protocols.</p>',
        '<p>By integrating DeFi into our traditional investment approach, we offer clients exposure to the next generation of financial infrastructure.</p>'
      ]
    },
    { id: 3, title: 'RWA Tokenisation', helpText: 'Real World Assets', useVideo: true, videoSrc: '/strategies-background.mp4', description: [
        '<p>We tokenize real-world assets to create new investment opportunities and improve liquidity for traditionally illiquid assets.</p>',
        '<p>Our RWA tokenization covers real estate, commodities, art, and other physical assets, making them accessible through blockchain technology.</p>',
        '<p>This approach democratizes access to high-value assets and creates new revenue streams for asset owners.</p>'
      ]
    },
    { id: 4, title: 'Risk Management', helpText: 'The Importance of Risk Management', img: '/strategies-quant.jpg', description: [
        '<p>Strong returns mean little without safeguards to protect them. Markets can shift quickly, and risk management ensures investors aren\'t overexposed when conditions turn.</p>',
        '<p>Our approach focuses on:</p>',
        '<ul><li>Diversification to spread exposure across strategies.</li><li>Position sizing that adapts to volatility and liquidity.</li><li>Downside protection through clear exit rules and allocation limits.</li><li>Ongoing monitoring to adjust as markets evolve.</li></ul>',
        '<p>Effective risk management smooths performance, limits losses, and helps investors stay invested long enough to capture lasting opportunities.</p>'
      ]
    },
    { id: 5, title: 'Managing Specific Risks', helpText: 'Risk Management', img: '/risk-riskmanagement.jpg', description: [
        'Our comprehensive risk management framework addresses the unique challenges of digital asset investing:',
        renderAccordion(risks, activeIndex, toggleAccordion, STRATEGIES_COLORS.MEDIUM_BLUE)
      ]
    }
  ];

  return (
    <>
      {/* CSS to eliminate white margins */}
      <style>
        {`
          .strategies_main {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
          }

          #strategies-cta {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
          }

          .full-width-dark-section, .full-width-medium-section {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
          }

          /* Target the outer div containers */
          body > div > div:last-child {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
          }

          /* Target any section that might be the last element */
          section:last-of-type {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
          }

          /* Target the strategies page specifically */
          div:has(#strategies-cta) {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
          }

          /* Force all elements to have no bottom margin/padding */
          * {
            margin-bottom: 0 !important;
          }

          /* Restore necessary margins for content readability */
          p, h1, h2, h3, h4, h5, h6 {
            margin-bottom: 1rem !important;
          }

          /* Remove margin from tagline section specifically */
          #strategies-tagline .h2 {
            margin-bottom: 0 !important;
          }

          /* But ensure the page itself has no bottom margin */
          html, body {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
          }

          /* Last resort: negative margin to pull footer up */
          #strategies-cta + * {
            margin-top: -20px !important;
          }

          /* Alternative: target the footer directly */
          footer {
            margin-top: -20px !important;
          }
        `}
      </style>

      <div>
      <section style={{
        backgroundColor: COLORS.DARK_BLUE,
        minHeight: '70vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative'
      }}>
        {/* Floating bubbles for hero section */}
        <div className="fx-bubbles" aria-hidden="true" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 0,
          pointerEvents: 'none'
        }}>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
        </div>

        <div className="container text-center" style={{ position: 'relative', zIndex: 1 }}>
          <h1 className="display-4 text-white mb-4">Strategies</h1>
          <p className="lead text-white mb-5">Innovative approaches to modern portfolio management</p>
        </div>
      </section>
      
      <div className="page-content-container" style={{
        marginTop: '-70px',
        position: 'relative',
        zIndex: 2
      }}>
        {/* Section Tagline */}
        <section id='strategies-tagline'
        className="full-width-medium-section"
        style={{ backgroundColor: STRATEGIES_COLORS.MEDIUM_BLUE, paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0' }}>
        <div className="container" style={{ paddingLeft: '20px', paddingRight: '20px' }}>
          <p className="h2 text-center" style={{ color: 'var(--text-medium-blue)' }}>Innovative strategies powering the future of capital allocation</p>
        </div>
      </section>

      {/* CTA Section #1 */}

      <section id='strategies-cta1' className="full-width-medium-section" style={{ backgroundColor: STRATEGIES_COLORS.MEDIUM_BLUE, paddingTop: '30px', paddingBottom: '30px', paddingLeft: '0', paddingRight: '0' }}>
        <div className="container text-center-mbl" style={{ paddingLeft: '20px', paddingRight: '20px', color: 'var(--text-medium-blue)' }}>
          <CTASection
            title="Sign up for the Moolah Capital Newsletter"
            cta="Sign Up"
            link="/addemail#email-signup"
            theme='signup'
            titleStyle={{ color: 'var(--text-medium-blue)' }}
          />
        </div>
      </section>

      {/* Strategy Sections with Alternating Backgrounds */}
      <section id="strategies-list">
        {cardStrategyList.map((strategy, index) => (
          <React.Fragment key={strategy.id}>
            <section className={`d-flex align-items-center ${index % 2 === 0 ? 'full-width-dark-section' : 'full-width-medium-section'}`} style={{
            backgroundColor: index % 2 === 0 ? STRATEGIES_COLORS.DARK_BLUE : STRATEGIES_COLORS.MEDIUM_BLUE,
            minHeight: strategy.useVideo ? '700px' : '500px', // Larger height for video sections
            paddingTop: '60px',
            paddingBottom: '60px',
            position: 'relative'
          }}>
            {/* Add floating bubbles to all sections - both dark navy and medium blue */}
            <div className="fx-bubbles" aria-hidden="true" style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '100%',
              zIndex: 0,
              pointerEvents: 'none'
            }}>
              <span className="b"></span>
              <span className="b"></span>
              <span className="b"></span>
              <span className="b"></span>
              <span className="b"></span>
            </div>

            <div className="container" style={{ color: index % 2 === 0 ? 'var(--text-white)' : 'var(--text-medium-blue)', paddingLeft: '20px', paddingRight: '20px', position: 'relative', zIndex: 1 }}>
              {strategy.title === 'Managing Specific Risks' ? (
                // Full width layout for Risk Management section (no image)
                <div className="row justify-content-center">
                  <div className="col-lg-10">
                    <div className="text-center mb-4">
                      <p className="h2 mb-0">{strategy.helpText}</p>
                    </div>
                    <div>
                      {strategy.description.map((desc, idx) => (
                        <div key={idx} className="mb-3">
                          {typeof desc === 'string' ? (
                            <p className="body-text text-center">{desc}</p>
                          ) : (
                            desc
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                // Regular layout with image for other sections
                <div
                  className={`row align-items-center ${index % 2 !== 0 ? 'flex-column flex-md-row' : 'flex-md-row-reverse flex-column'}`}
                >
                  <div className={`${strategy.useVideo ? 'col-lg-6' : 'col-md-6'} my-3 d-flex align-items-center justify-content-center`}>
                    <div className="w-100 px-4">
                      <div className="text-start mb-4">
                        <p className="h2 mb-0 text-center">{strategy.helpText}</p>
                      </div>
                      <div>
                        {strategy.description.map((desc, idx) => (
                          <div key={idx} className="mb-3" dangerouslySetInnerHTML={{ __html: desc }}></div>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className={`${strategy.useVideo ? 'col-lg-6' : 'col-md-6'} my-3 d-flex align-items-center justify-content-center`}>
                    <div className="fund-image-container" style={{ maxWidth: strategy.useVideo ? '600px' : '600px', width: '100%' }}>
                      {strategy.useVideo ? (
                        <video
                          src={strategy.videoSrc}
                          autoPlay
                          loop
                          muted
                          playsInline
                          style={{ maxWidth: '100%', maxHeight: '600px', objectFit: 'cover', width: '100%', borderRadius: '12px' }}
                        />
                      ) : (
                        <img
                          src={strategy.img}
                          alt={strategy.title}
                          className="img-fluid rounded no-hover"
                          style={{ maxHeight: '400px', width: '100%', objectFit: 'cover' }}
                        />
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </section>

          {/* Insert CTA section after "Risk Management" (id: 5) */}
          {strategy.id === 5 && (
            <section id='strategies-cta' className="full-width-medium-section" style={{ backgroundColor: STRATEGIES_COLORS.MEDIUM_BLUE, paddingTop: '30px', paddingBottom: '30px', paddingLeft: '0', paddingRight: '0', position: 'relative' }}>
              {/* Floating bubbles for CTA section */}
              <div className="fx-bubbles" aria-hidden="true" style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                zIndex: 0,
                pointerEvents: 'none'
              }}>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
              </div>

              <div className="container text-center-mbl" style={{ paddingLeft: '20px', paddingRight: '20px', position: 'relative', zIndex: 1 }}>
                <CTASection
                  title="Let's Build Smarter Portfolios - Together"
                  cta="View Funds"
                  link="/funds"
                  theme='learn'
                  titleStyle={{ color: 'var(--text-medium-blue)' }}
                />
              </div>
            </section>
          )}
        </React.Fragment>
        ))}
      </section>


      </div>
    </div>
    </>
  );
};

export default Strategies;
