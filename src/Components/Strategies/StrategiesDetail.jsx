import { useParams } from 'react-router-dom';

import Hedging from './StrategiesList/Hedging.jsx';
import EmergingTrends from './StrategiesList/EmergingTrends.jsx';
import Diversification from './StrategiesList/Diversification.jsx';
import QuantitativeModels from './StrategiesList/QuantitativeModels.jsx';
import AppliedIntelligence from './StrategiesList/AppliedIntelligence.jsx';
import StressTestingScenarioAnalysis from './StrategiesList/ScenarioAnalysis.jsx';

const StrategiesDetail = () => {

	const { strategyId } = useParams();  // get the strategyId

	// strategy data 
	const strategyList = [
		{ id: 'hedging', component: <Hedging /> },
		{ id: 'emerging-trends', component: <EmergingTrends /> },
		{ id: 'diversification', component: <Diversification /> },
		{ id: 'quantitative-models', component: <QuantitativeModels /> },
		{ id: 'applied-intelligence', component: <AppliedIntelligence /> },
		{ id: 'scenario-analysis', component: <StressTestingScenarioAnalysis /> },
	];

	// Find the fund that matches the fundId
	const strategy = strategyList.find(strategy => strategy.id === strategyId);

	// If no fund matches, show a "not found" message
	if (!strategy) {
		return <div className='text-black py-5 my-5 display-4 fw-bold'>Strategy not found</div>;
	}

	return (
		<div>
			{strategy.component}
		</div>
	);
}

export default StrategiesDetail;
