import React from 'react';
import { render, screen, waitFor, within } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import Strategies from '../Strategies';

// Set the timeout for all tests in this file
jest.setTimeout(20000);

describe('Strategies Component', () => {
  const renderComponent = () => 
    render(
      <BrowserRouter>
        <Strategies />
      </BrowserRouter>
    );

  test.skip('renders the video container', async () => {
    jest.setTimeout(10000); // 10 seconds timeout for this test

    renderComponent();

    const videoContainer = screen.getByTestId('video-container');
    expect(videoContainer).toBeInTheDocument();
  });

  test('displays loading spinner initially', () => {
    renderComponent();

    // Check for spinner element with specific role
    const spinner = screen.getByRole('status');
    expect(spinner).toBeInTheDocument();

    // Check for loading text in the spinner container
    const loadingContainer = spinner.closest('.spinner-container') || spinner.parentElement;
    const loadingTexts = within(loadingContainer).queryAllByText((content, element) => {
      return element.textContent.includes('Loading...');
    });
    expect(loadingTexts.length).toBeGreaterThan(0);
  });

  test('displays all strategy cards', async () => {
    jest.setTimeout(15000); // 15 seconds timeout for this test

    renderComponent();
    
    // Check for specific heading text
    expect(screen.getByRole('heading', { name: /Innovative Strategies Powering the Future of Capital Allocation/i })).toBeInTheDocument();
    expect(screen.getByText(/scroll down/i)).toBeInTheDocument();

    // Check for all strategy titles
    //expect(screen.getByText(/Applied Intelligence/i)).toBeInTheDocument();
    //expect(screen.getByText(/Emerging Trends/i)).toBeInTheDocument();
    //expect(screen.getByText(/Scenario Analysis/i)).toBeInTheDocument();
    //expect(screen.getByText(/Position Hedging/i)).toBeInTheDocument();
    //expect(screen.getByText(/Quantitative Models/i)).toBeInTheDocument();
    //expect(screen.getByText(/Diversification/i)).toBeInTheDocument();
  });

  test.skip('displays strategy descriptions', async () => {
    jest.setTimeout(15000); // 10 seconds timeout for this test

    renderComponent();
    
    // Wait for content to load after video
    await waitFor(() => {
      expect(screen.queryByText('Loading ...')).not.toBeInTheDocument();
    }, { timeout: 19000 });
    
    // Check for specific text from descriptions
    expect(screen.getByText(/We integrate advanced data analytics/i)).toBeInTheDocument();
    expect(screen.getByText(/Stablecoin expansion into traditional finance/i)).toBeInTheDocument();
    expect(screen.getByText(/By modeling different scenarios/i)).toBeInTheDocument();
  });

  test.skip('has accessible links with descriptive text', async () => {
    jest.setTimeout(10000); // 10 seconds timeout for this test
    renderComponent();
    
    // Wait for content to load after video
    await waitFor(() => {
      expect(screen.queryByText('Loading ...')).not.toBeInTheDocument();
    }, { timeout: 9000 });
    
    // Get the scroll indicator link
    const scrollLink = screen.getByText('scroll down').closest('a');
    expect(scrollLink).toHaveAttribute('href', '#moolah-strategies');
    
    // All links should have accessible text or aria-labels
    const links = screen.getAllByRole('link');
    links.forEach(link => {
      const hasText = link.textContent.trim().length > 0;
      const hasAriaLabel = link.getAttribute('aria-label');
      const hasTitle = link.getAttribute('title');
      
      expect(hasText || hasAriaLabel || hasTitle).toBeTruthy();
    });
  });

  test('has proper image alt text for all images', async () => {
    jest.setTimeout(10000); // 10 seconds timeout for this test

    // Wait for content to load after video
    await waitFor(() => {
      expect(screen.queryByText('Loading ...')).not.toBeInTheDocument();
    }, { timeout: 8950 });
  });

  test.skip('has proper image alt text for all images', async () => {
    renderComponent();
    
    // Wait for content to load after video
    await waitFor(() => {
      expect(screen.queryByText('Loading ...')).not.toBeInTheDocument();
    }, { timeout: 18500 });
    
    const images = screen.queryAllByRole('img');
    images.forEach(img => {
      expect(img).toHaveAttribute('alt');
      expect(img.alt.trim().length).toBeGreaterThan(0);
    });
  });

  test.skip('removes loading spinner after video can play', async () => {
    renderComponent();
    
    // Initially spinner is visible
    expect(screen.getByRole('status')).toBeInTheDocument();
    
    // Simulate the video canplay event
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    }, { timeout: 1000 });
  });

  test('displays content after scrolling', async () => {
    renderComponent();
    
    // Wait for initial content to load
    await waitFor(() => {
      // Check for any element that should be present initially
      expect(screen.getByText(/Investment Strategies/i)).toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Mock the IntersectionObserver to simulate scrolling
    const mockIntersectionObserver = window.IntersectionObserver;
    const mockInstance = {
      observe: jest.fn(),
      unobserve: jest.fn(),
      disconnect: jest.fn()
    };
    
    // Get all the callback functions that were registered with IntersectionObserver
    const observerCallbacks = [];
    window.IntersectionObserver = jest.fn((callback) => {
      observerCallbacks.push(callback);
      return mockInstance;
    });
    
    // Re-render to use our mocked IntersectionObserver
    renderComponent();
    
    // Trigger all intersection callbacks with isIntersecting=true
    observerCallbacks.forEach(callback => {
      callback([{ isIntersecting: true, target: document.createElement('div') }]);
    });
    
    // Wait for any content that should be visible after "scrolling"
    await waitFor(() => {
      // Try multiple possible elements that might be visible
      const possibleElements = [
        screen.queryByText(/Applied Intelligence/i),
      ];
      
      // Check if at least one of these elements is visible
      const visibleElement = possibleElements.find(el => el !== null);
      expect(visibleElement).not.toBeNull();
    }, { timeout: 5000 });
    
    // Restore the original IntersectionObserver
    window.IntersectionObserver = mockIntersectionObserver;
  });
});
