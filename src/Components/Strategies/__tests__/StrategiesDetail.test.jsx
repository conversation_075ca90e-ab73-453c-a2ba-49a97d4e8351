import React from 'react';
import { render, screen } from '@testing-library/react';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import StrategiesDetail from '../StrategiesDetail';

// Mock the strategy components
jest.mock('../StrategiesList/Hedging', () => {
  return function MockHedging() {
    return <div data-testid="hedging-strategy">Hedging Strategy Content</div>;
  };
});

jest.mock('../StrategiesList/EmergingTrends', () => {
  return function MockEmergingTrends() {
    return <div data-testid="emerging-trends-strategy">Emerging Trends Strategy Content</div>;
  };
});

jest.mock('../StrategiesList/Diversification', () => {
  return function MockDiversification() {
    return <div data-testid="diversification-strategy">Diversification Strategy Content</div>;
  };
});

jest.mock('../StrategiesList/QuantitativeModels', () => {
  return function MockQuantitativeModels() {
    return <div data-testid="quantitative-models-strategy">Quantitative Models Strategy Content</div>;
  };
});

jest.mock('../StrategiesList/AppliedIntelligence', () => {
  return function MockAppliedIntelligence() {
    return <div data-testid="applied-intelligence-strategy">Applied Intelligence Strategy Content</div>;
  };
});

jest.mock('../StrategiesList/ScenarioAnalysis', () => {
  return function MockScenarioAnalysis() {
    return <div data-testid="scenario-analysis-strategy">Scenario Analysis Strategy Content</div>;
  };
});

describe('StrategiesDetail Component', () => {
  function renderWithRouter(strategyId) {
    return render(
      <MemoryRouter initialEntries={[`/strategies/${strategyId}`]}>
        <Routes>
          <Route path="/strategies/:strategyId" element={<StrategiesDetail />} />
        </Routes>
      </MemoryRouter>
    );
  }

  test('renders hedging strategy when strategyId is "hedging"', () => {
    renderWithRouter('hedging');
    expect(screen.getByTestId('hedging-strategy')).toBeInTheDocument();
  });

  test('renders emerging-trends strategy when strategyId is "emerging-trends"', () => {
    renderWithRouter('emerging-trends');
    expect(screen.getByTestId('emerging-trends-strategy')).toBeInTheDocument();
  });

  test('renders diversification strategy when strategyId is "diversification"', () => {
    renderWithRouter('diversification');
    expect(screen.getByTestId('diversification-strategy')).toBeInTheDocument();
  });

  test('renders quantitative-models strategy when strategyId is "quantitative-models"', () => {
    renderWithRouter('quantitative-models');
    expect(screen.getByTestId('quantitative-models-strategy')).toBeInTheDocument();
  });

  test('renders applied-intelligence strategy when strategyId is "applied-intelligence"', () => {
    renderWithRouter('applied-intelligence');
    expect(screen.getByTestId('applied-intelligence-strategy')).toBeInTheDocument();
  });

  test('renders scenario-analysis strategy when strategyId is "scenario-analysis"', () => {
    renderWithRouter('scenario-analysis');
    expect(screen.getByTestId('scenario-analysis-strategy')).toBeInTheDocument();
  });

  test('displays "Strategy not found" message for invalid strategyId', () => {
    renderWithRouter('invalid-strategy');
    expect(screen.getByText('Strategy not found')).toBeInTheDocument();
  });
});
