import React, { useRef, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import './VideoComponent.css';

const VideoComponent = () => {
	const videoRef = useRef(null);
	const [hasError, setHasError] = useState(false);
	const [videoLoaded, setVideoLoaded] = useState(false);

	const handleMetadataLoad = () => {
		if (videoRef.current) {
			videoRef.current.playbackRate = 0.3;
			console.log(videoRef.current.playbackRate);
		} else {
			if (videoRef) {
				videoRef.playbackRate = 0.3;
				console.log(videoRef.playbackRate);
			}
		}
	};

	useEffect(() => {
		const video = videoRef.current;
		if (!video) return;

		const handleCanPlay = () => {
			video.play().catch((err) => {
				console.warn('Autoplay blocked:', err);
				setHasError(true);
			});
			setVideoLoaded(true);
		};

		const handleError = () => {
			console.error('Video failed to load.');
			setHasError(true);
		};

		video.addEventListener('canplay', handleCanPlay);
		video.addEventListener('error', handleError);

		return () => {
			video.removeEventListener('canplay', handleCanPlay);
			video.removeEventListener('error', handleError);
		};
	}, []);

	return (
		<div className="video-container">
			{!videoLoaded && !hasError && (
				<div 
					className="video-placeholder"
					style={{
						backgroundImage: 'url(/loading-home.jpg)'
					}}
				>
					<div className="spinner-border text-light" role="status">
						<span className="visually-hidden">Loading video...</span>
					</div>
				</div>
			)}

			<video
				autoPlay
				className="background-video"
				loop
				muted
				onLoadedMetadata={handleMetadataLoad}
				playsInline
				preload="auto"
				ref={videoRef}
				src="/moolah-home.mp4"
				style={{ visibility: videoLoaded ? 'visible' : 'hidden' }}
			/>

			{videoLoaded && !hasError && (
				<motion.div
					className="video-overlay-content"
					initial={{ opacity: 0, y: 30 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: window.innerWidth < 768 ? 0.6 : 1 }}
				>
					<div className="body-text hero-main-content text-center">
						<div className="h1">Simple and Successful</div>
						<div className="h1">Crypto Investing</div>
					</div>
				</motion.div>
			)}
		</div>
	);
};

export default VideoComponent;
