/* Spacing Variables - Design System Tokens */
:root {
  /* Base Spacing Scale */
  --spacing-xs: 8px;        /* Extra small spacing */
  --spacing-sm: 12px;       /* Small spacing */
  --spacing-md: 16px;       /* Medium spacing */
  --spacing-lg: 24px;       /* Large spacing */
  --spacing-xl: 32px;       /* Extra large spacing */
  --spacing-xxl: 48px;      /* Extra extra large spacing */

  /* Section Spacing */
  --spacing-section: 120px;        /* Large section spacing */

  /* Container Padding */
  --container-padding-x: 20px;     /* Horizontal container padding */
  --container-padding-y: 60px;     /* Vertical container padding */

  /* Section Padding (using spacing-section for consistency) */
  --section-padding-y: var(--spacing-section);  /* Large section padding */
  --section-padding-small: 30px;   /* Small section padding for CTAs */

  /* Layout Variables */
  --navbar-height: 80px;           /* Global navbar height - prevents hero overlap */
  --tabs-header-height: 48px;      /* Height of sticky tabs header */
  --hero-padding-top: 120px;       /* Extra padding between navbar and hero title */
  --section-content-padding: 32px; /* Consistent horizontal padding for section text content */
  --section-half-padding: 48px;    /* Prevents content from crossing imaginary center line */

  /* Component Spacing (for future use) */
  --card-padding: var(--spacing-lg);
  --button-padding-x: var(--spacing-md);
  --button-padding-y: var(--spacing-sm);

  /* Component Dimensions */
  --chart-width-small: 400px;      /* Small chart container width */
  --chart-width-large: 1200px;     /* Large chart container width */
  --chart-height-default: 600px;   /* Default chart height */
  --image-height-default: 300px;   /* Default image height */
  --cta-min-height: 150px;         /* Minimum height for CTA sections */
  --cta-width-half: 45%;           /* Half width for side-by-side CTAs */
  --cta-min-width: 250px;          /* Minimum width for CTAs */
  --chart-legend-top: 120px;       /* Top position for chart legends */
}
