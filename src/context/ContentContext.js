import React, { createContext, useContext, useState } from 'react';

// Import all content data
import fundsData from '../data/funds.json';
import navigationData from '../data/navigation.json';
import faqData from '../data/faq.json';
import aboutData from '../data/about.json';
import strategiesData from '../data/strategies.json';

// Create the content context
const ContentContext = createContext();

// Custom hook for using the content context
export const useContent = () => {
  const context = useContext(ContentContext);
  if (!context) {
    throw new Error('useContent must be used within a ContentProvider');
  }
  return context;
};

// Content provider component
export const ContentProvider = ({ children }) => {
  const [content, setContent] = useState({
    funds: fundsData.funds,
    navigation: navigationData,
    faq: faqData.sections,
    about: aboutData,
    strategies: strategiesData.strategies,
  });

  // Function to update content (could be used with an API in the future)
  const updateContent = (section, newData) => {
    setContent(prevContent => ({
      ...prevContent,
      [section]: newData
    }));
  };

  return (
    <ContentContext.Provider value={{ content, updateContent }}>
      {children}
    </ContentContext.Provider>
  );
};

export default ContentContext;
