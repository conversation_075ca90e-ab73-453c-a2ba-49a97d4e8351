import { useState, useEffect } from 'react';

const imageCache = new Map();

const useAssetCache = (key, srcPath) => {
  const [asset, setAsset] = useState(() => imageCache.get(key) || null);

  useEffect(() => {
    if (imageCache.has(key)) {
      setAsset(imageCache.get(key));
      return;
    }

    const img = new Image();
    img.src = srcPath;

    img.onload = () => {
      const result = { src: srcPath, status: 'loaded' };
      imageCache.set(key, result);
      setAsset(result);
    };

    img.onerror = () => {
      const result = { src: '', status: 'error' };
      imageCache.set(key, result);
      setAsset(result);
    };
  }, [key, srcPath]);

  return asset;
};

export default useAssetCache;
