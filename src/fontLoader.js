/**
 * Font loading optimization strategy
 * This file implements a progressive font loading strategy to improve performance
 */

// Define the fonts we want to load and monitor
const fontFamilies = [
  { family: 'Söhne', weight: '300' },
  { family: 'Libre Baskerville', weight: '400' },
  { family: 'Libre Baskerville', weight: '700' },
  { family: 'Lato', weight: '400' },
  { family: 'Lato', weight: '700' },
  { family: 'Instrument Serif', weight: '400' },
  { family: 'Lexend', weight: '400' },
  { family: 'Lexend', weight: '700' }
];

// Function to add a class to the document when fonts are loaded
function markFontsLoaded() {
  document.documentElement.classList.add('fonts-loaded');
}

// Check if fonts are already cached
if (sessionStorage.fontsLoaded) {
  // Apply fonts immediately if they're cached
  markFontsLoaded();
} else {
  // Create an array of promises for each font
  const fontPromises = fontFamilies.map(font => 
    document.fonts.load(`${font.weight} 1em "${font.family}"`)
  );
  
  // Wait for all fonts to load
  Promise.all(fontPromises)
    .then(() => {
      markFontsLoaded();
      // Cache the result
      sessionStorage.fontsLoaded = true;
    })
    .catch(err => {
      console.warn('Font loading failed, using system fonts:', err);
      // Still mark as loaded to avoid perpetual loading state
      markFontsLoaded();
    });
}

// Export for potential future use
//export default { markFontsLoaded };
const fontLoader = { markFontsLoaded };
export default fontLoader;
