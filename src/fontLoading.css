/* Font loading strategy styles */

/* Before custom fonts load, use system fonts */
body {
  font-family: "<PERSON><PERSON><PERSON><PERSON>", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; /* <PERSON><PERSON><PERSON><PERSON> first, then system font fallback */
}

.nav-link,
.btn,
.lato-regular,
.lato-bold,
.lato-light,
.body-text,
.body-text-small,
.body-text-large,
.body-text-center,
.navbar-brand {
  font-family: "Söhne", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; /* <PERSON><PERSON><PERSON><PERSON> first, then system font fallback */
}

code {
  font-family: Consolas, Monaco, 'Andale Mono', monospace; /* System font fallback for --font-code */
}

.brand-name,
.navbar-brand {
  font-family: "Söhne", system-ui, sans-serif; /* <PERSON><PERSON><PERSON><PERSON> first, then system font fallback */
}

/* Once fonts are loaded, apply the web fonts */
.fonts-loaded body {
  font-family: var(--font-header);
}

.fonts-loaded .body-text,
.fonts-loaded .body-text-small,
.fonts-loaded .body-text-large,
.fonts-loaded .body-text-center,
.fonts-loaded .nav-link,
.fonts-loaded .btn,
.fonts-loaded .lato-regular,
.fonts-loaded .lato-bold,
.fonts-loaded .lato-light,
.fonts-loaded .navbar-brand {
  font-family: var(--font-body);
}

.fonts-loaded code {
  font-family: var(--font-code);
}

.fonts-loaded .brand-name,
.fonts-loaded .navbar-brand {
  font-family: var(--font-brand);
}

/* Add a very subtle transition when fonts load to avoid jarring layout shifts */
body, p, h1, h2, h3, h4, h5, h6, .btn, .nav-link {
  transition: font-family 0.1s ease-out;
}
