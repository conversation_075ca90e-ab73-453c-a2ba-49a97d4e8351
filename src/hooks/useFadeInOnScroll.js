import { useEffect, useState, useRef } from 'react';

const useFadeInOnScroll = (delay = 0) => {
  const domRef = useRef();
  const [isVisible, setVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            setVisible(true);
          }, delay);
        }
      });
    });
    
    // Store the current value of domRef.current in a variable
    const currentDomRef = domRef.current;
    
    if (currentDomRef) {
      observer.observe(currentDomRef);
    }
    
    return () => {
      // Use the stored reference in the cleanup function
      if (currentDomRef) {
        observer.unobserve(currentDomRef);
      }
    };
  }, [delay]);

  return [domRef, isVisible];
};

export default useFadeInOnScroll;
