import { useEffect, useRef } from 'react';

function useVideoPlayer(options = {}) {
  const { autoPlay = true, playbackRate = 1, delay = 250 } = options;
  const videoRef = useRef(null);
  
  useEffect(() => {
    const video = videoRef.current;
    
    const handleCanPlay = () => {
      setTimeout(() => {
        console.log("loading ...");
      }, delay);
      
      if (video && autoPlay) video.play();
    };
    
    if (video) {
      video.addEventListener('canplay', handleCanPlay);
      if (playbackRate !== 1) {
        video.playbackRate = playbackRate;
        console.log("Video pplayback rate "+video.playbackRate);
      }
    }
    
    return () => {
      if (video) {
        video.removeEventListener('canplay', handleCanPlay);
      }
    };
  }, [autoPlay, playbackRate, delay]);
  
  return videoRef;
}

export default useVideoPlayer;