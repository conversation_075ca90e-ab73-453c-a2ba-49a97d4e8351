import { useEffect } from 'react';

const useVisibilityObserver = (refs, setVisibleArray) => {
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const index = parseInt(entry.target.getAttribute('data-index'), 10);
          if (entry.isIntersecting) {
            setVisibleArray((prev) => {
              const updated = [...prev];
              updated[index] = true;
              return updated;
            });
          }
        });
      },
      { threshold: 0.1 }
    );

    refs.forEach((ref, i) => {
      if (ref.current) {
        ref.current.setAttribute('data-index', i);
        observer.observe(ref.current);
      }
    });

    return () => observer.disconnect();
  }, [refs, setVisibleArray]);
};

export default useVisibilityObserver;
