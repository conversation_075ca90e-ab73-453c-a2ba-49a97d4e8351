/* Font imports - optimized for performance */
@import url('https://fonts.googleapis.com/css2?family=Instrument+Serif&family=Lato:wght@100;300;400;700;900&family=Libre+Baskerville:wght@400;700&family=Lexend:wght@400;500;600;700&display=swap');

/* Global design system variables */
@import './constants/colors.css';
@import './constants/spacing.css';

html {
  scroll-behavior: smooth !important;
}

/* Base image styles */
img {
  border-radius: 0px !important;
  transition: transform 0.3s ease;
}

/* Base image styles - no shadows */
img:not([src*="loading"]):not([src*="logo"]):not([src*="icon"]):not(.navbar-brand img):not(.navbar img):not(.hero-section img):not(.header-section img):not(.top-section img):not(.footer img):not(.no-hover):not(.strategy-image):not(.fund-image) {
  /* Removed red shadow */
}

/* Apply hover effect to images - subtle scale only */
img:not([src*="loading"]):not([src*="logo"]):not([src*="icon"]):not(.navbar-brand img):not(.navbar img):not(.hero-section img):not(.header-section img):not(.top-section img):not(.footer img):not(.no-hover):hover,
.strategy-image:hover,
.fund-image:hover {
  transform: scale(1.05);
  /* Removed red shadow */
}

/* Add this to your global styles */
.no-break-title {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 0.2em !important;
}

.title-text, 
.brand-name {
  white-space: nowrap !important;
  display: inline-block !important;
}

@media (max-width: 768px) {
  .no-break-title {
    gap: 0.15em !important;
  }
}

/* Video styles for full-screen background videos */
.hero-background-video,
.background-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}

/* Hero section positioning */
.full-height-hero {
  position: relative;
  overflow: hidden;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  transition: opacity 0.3s ease;
}

.hero-background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  transition: opacity 0.3s ease;
}

.hero-content {
  position: relative;
  z-index: 2;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
  color: white;
  text-align: center;
  font-size: 0.9rem;
  opacity: 0.8;
}

.scroll-indicator i {
  display: block;
  margin-bottom: 5px;
  font-size: 1.2rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}
