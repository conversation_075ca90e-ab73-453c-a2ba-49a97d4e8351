// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

// Mock window.scrollTo
window.scrollTo = jest.fn();

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor(callback) {
    this.callback = callback;
  }

  observe(element) {
    // Simulate an intersection
    this.callback([
      {
        isIntersecting: true,
        target: element
      }
    ]);
  }

  unobserve() {
    // Do nothing
  }

  disconnect() {
    // Do nothing
  }
};

// Mock HTMLMediaElement.prototype.play
HTMLMediaElement.prototype.play = jest.fn().mockImplementation(() => {
  return Promise.resolve();
});

// Mock HTMLMediaElement.prototype.pause
HTMLMediaElement.prototype.pause = jest.fn();

// Add any other global mocks needed for tests
