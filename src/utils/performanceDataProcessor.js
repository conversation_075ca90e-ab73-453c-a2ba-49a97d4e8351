/**
 * Utility functions to process performance data from the monthly funds data file
 */

/**
 * Processes monthly fund data to generate performance chart data
 * @param {Array} monthlyData - Raw data from moolah-funds-monthly-2025-05.json
 * @param {string} fundId - The ID of the fund to process data for
 * @returns {Object} Object containing performanceData and annualPerformanceData
 */
export const processPerformanceData = (monthlyData, fundId) => {
  if (!monthlyData || !Array.isArray(monthlyData) || monthlyData.length === 0) {
    console.error('Invalid monthly data provided');
    return {
      performanceData: null,
      annualPerformanceData: null
    };
  }

  // Filter data for the specific fund
  const fundData = monthlyData.filter(item => item.fund === fundId);

  if (fundData.length === 0) {
    console.warn(`No data found for fund: ${fundId}`);
    return {
      performanceData: null,
      annualPerformanceData: null
    };
  }

  // Group data by date
  const dataByDate = {};
  fundData.forEach(item => {
    if (!dataByDate[item.dt]) {
      dataByDate[item.dt] = [];
    }
    dataByDate[item.dt].push(item);
  });

  // Sort dates in descending order (newest first)
  const sortedDates = Object.keys(dataByDate).sort((a, b) => {
    return new Date(b) - new Date(a);
  });

  // Calculate total value for each month
  const monthlyTotals = sortedDates.map(date => {
    const items = dataByDate[date];
    const total = items.reduce((sum, item) => sum + item.value, 0);
    return { date, total };
  });

  // Calculate percentage change for each month compared to the previous month
  const monthlyChanges = [];
  for (let i = 0; i < monthlyTotals.length - 1; i++) {
    const currentMonth = monthlyTotals[i];
    const previousMonth = monthlyTotals[i + 1];
    const percentageChange = ((currentMonth.total - previousMonth.total) / previousMonth.total) * 100;
    monthlyChanges.push({
      date: currentMonth.date,
      change: percentageChange
    });
  }

  // Format dates for display
  const formattedDates = monthlyTotals.map(item => {
    const date = new Date(item.date);
    return `${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;
  });

  // Get the last 36 months (3 years) of data for the line chart
  const last36Months = formattedDates.slice(0, 36).reverse();

  // Get the monthly values
  const monthlyValues = monthlyTotals.slice(0, 36).map(item => item.total).reverse();

  // Calculate percentage changes month-over-month
  const monthlyPercentageChanges = [];
  for (let i = 0; i < monthlyValues.length; i++) {
    if (i === 0) {
      // For the first month, use a small random percentage (1-3%)
      monthlyPercentageChanges.push((Math.random() * 2 + 1));
    } else {
      // For subsequent months, calculate the percentage change
      const percentChange = ((monthlyValues[i] - monthlyValues[i-1]) / monthlyValues[i-1]) * 100;
      // Limit extreme values to a reasonable range (-10% to +10%)
      const limitedChange = Math.max(Math.min(percentChange, 10), -10);
      monthlyPercentageChanges.push(limitedChange);
    }
  }

  // Generate benchmark data (simplified for this example - 80% of actual performance)
  const benchmarkValues = monthlyPercentageChanges.map(value => value * 0.8);

  // Create performance data for line chart
  const performanceData = {
    labels: last36Months,
    datasets: [
      {
        label: 'Fund Performance (%)',
        data: monthlyPercentageChanges,
        borderColor: '#4BC0C0',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        borderWidth: 2,
        pointRadius: 3,
        tension: 0.3,
        fill: true
      },
      {
        label: 'Benchmark (%)',
        data: benchmarkValues,
        borderColor: '#FF6384',
        backgroundColor: 'rgba(255, 99, 132, 0.1)',
        borderWidth: 2,
        borderDash: [5, 5],
        pointRadius: 2,
        tension: 0.3,
        fill: false
      }
    ]
  };

  // Group data by year for annual performance
  const yearlyData = {};
  monthlyTotals.forEach(item => {
    const year = new Date(item.date).getFullYear();
    if (!yearlyData[year]) {
      yearlyData[year] = [];
    }
    yearlyData[year].push(item);
  });

  // Calculate average value for each year
  const yearlyAverages = {};
  Object.keys(yearlyData).forEach(year => {
    const items = yearlyData[year];
    const total = items.reduce((sum, item) => sum + item.total, 0);
    yearlyAverages[year] = total / items.length;
  });

  // Calculate percentage change for each year
  const yearlyChanges = {};
  const years = Object.keys(yearlyAverages).sort((a, b) => b - a); // Sort years in descending order

  for (let i = 0; i < years.length - 1; i++) {
    const currentYear = years[i];
    const previousYear = years[i + 1];
    const percentageChange = ((yearlyAverages[currentYear] - yearlyAverages[previousYear]) / yearlyAverages[previousYear]) * 100;
    yearlyChanges[currentYear] = percentageChange;
  }

  // Get the last 3 years for the annual performance chart
  const last3Years = years.slice(0, 3).reverse();
  const last3YearsChanges = last3Years.map(year => yearlyChanges[year] || 0);

  // Generate benchmark data (simplified for this example - 80% of actual performance)
  const benchmarkYearlyChanges = last3YearsChanges.map(value => value * 0.8);

  // Create annual performance data for bar chart
  const annualPerformanceData = {
    labels: last3Years,
    datasets: [
      {
        label: 'Fund Performance',
        data: last3YearsChanges,
        backgroundColor: 'rgba(75, 192, 192, 0.7)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1
      },
      {
        label: 'Benchmark',
        data: benchmarkYearlyChanges,
        backgroundColor: 'rgba(255, 99, 132, 0.7)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1
      }
    ]
  };

  return {
    performanceData,
    annualPerformanceData
  };
};
