/**
 * Scrolls to an element with proper offset for the navbar
 * @param {string} elementId - The ID of the element to scroll to
 * @param {number} offset - Additional offset in pixels (default: 0)
 */
export const scrollToElement = (elementId, offset = 0) => {
  const element = document.getElementById(elementId);
  
  if (element) {
    // Base navbar height plus any additional offset
    const navbarHeight = 80 + offset; // Reduced from 90px to 80px
    const elementPosition = element.getBoundingClientRect().top;
    const offsetPosition = elementPosition + window.pageYOffset - navbarHeight;
    
    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    });
  } else {
    console.warn(`Element with id "${elementId}" not found`);
  }
};

/**
 * Handles anchor links with proper offset
 * @param {Event} e - The click event
 * @param {string} hash - The hash part of the URL (e.g., "#section-id")
 */
export const handleAnchorClick = (e, hash) => {
  e.preventDefault();
  const id = hash.replace('#', '');
  scrollToElement(id);
};
